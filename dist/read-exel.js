"use strict";
const XLSX = require('xlsx');
const CreateDataImport = () => {
    const webhook = XLSX.readFile('./tester.xlsx');
    const sheetName = webhook.SheetNames[9];
    const data = XLSX.utils.sheet_to_json(webhook.Sheets[sheetName], {
        raw: false,
    });
    const array = [
        {
            name: String(data[2].__EMPTY_2).split('T')[0],
            current_balance: 0,
            amounts: data[2].__EMPTY_3,
            transaction_date_time: data[2].__EMPTY_2,
            wallet_id: 3,
            topic_id: 26,
            status_id: 2,
            create_at: data[2].__EMPTY_2,
            update_at: data[2].__EMPTY_2,
        },
        {
            name: String(data[2].__EMPTY_2).split('T')[0],
            current_balance: 0,
            amounts: data[2].__EMPTY_4,
            transaction_date_time: data[2].__EMPTY_2,
            wallet_id: 3,
            topic_id: 17,
            status_id: 2,
            create_at: data[2].__EMPTY_2,
            update_at: data[2].__EMPTY_2,
        },
    ];
    console.log(array);
    return array;
};
CreateDataImport();
