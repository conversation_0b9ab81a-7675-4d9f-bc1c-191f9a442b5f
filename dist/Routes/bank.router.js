"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const router = (0, express_1.Router)();
const { List, Create, Update, Remove, } = require('../Controller/bank.controller');
const { auth } = require('../Middleware/auth.middleware');
const PATH = `/bank`;
router.get(`${PATH}`, auth, List);
router.post(`${PATH}`, auth, Create);
router.put(`${PATH}/:id`, auth, Update);
router.delete(`${PATH}/:id`, auth, Remove);
module.exports = router;
