"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const router = (0, express_1.Router)();
const { MonthViewList, YearViewList, TotalViewList, Summary, } = require('../Controller/schedule.controller');
const { auth } = require('../Middleware/auth.middleware');
const PATH = `/schedule`;
router.get(`${PATH}/month`, auth, MonthViewList);
router.get(`${PATH}/year`, auth, YearViewList);
router.get(`${PATH}/summary`, auth, Summary);
router.get(`${PATH}/total`, auth, TotalViewList);
module.exports = router;
