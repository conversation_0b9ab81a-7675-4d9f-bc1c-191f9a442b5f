"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const router = (0, express_1.Router)();
const { register, login, getMe } = require('../Controller/auth.controller');
const { auth } = require('../Middleware/auth.middleware');
router.post('/register', register);
router.post('/login', login);
router.get('/me', auth, getMe);
module.exports = router;
