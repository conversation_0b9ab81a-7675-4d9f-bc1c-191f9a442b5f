"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const router = (0, express_1.Router)();
const { List, Detail, Update, Remove, } = require('../Controller/user.controller');
const { auth } = require('../Middleware/auth.middleware');
const PATH = `/user`;
router.get(`${PATH}`, auth, List);
router.get(`${PATH}/:id`, auth, Detail);
router.put(`${PATH}/:id`, auth, Update);
router.delete(`${PATH}/:id`, auth, Remove);
module.exports = router;
