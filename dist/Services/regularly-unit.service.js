"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteRegularlyUnit = exports.updateRegularlyUnit = exports.createRegularlyUnit = exports.getRegularlyUnitList = void 0;
const client_1 = require("@prisma/client");
const time_1 = require("../Utils/time");
const prisma = new client_1.PrismaClient();
const getRegularlyUnitList = () => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.regularly_unit.findMany({
        include: {
            regularly: true,
        },
    });
    return response;
});
exports.getRegularlyUnitList = getRegularlyUnitList;
const createRegularlyUnit = (data) => __awaiter(void 0, void 0, void 0, function* () {
    data.create_at = (0, time_1.current_datetime)();
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.regularly_unit.create({
        data: data,
    });
    return response;
});
exports.createRegularlyUnit = createRegularlyUnit;
const updateRegularlyUnit = (id, data) => __awaiter(void 0, void 0, void 0, function* () {
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.regularly_unit.update({
        where: {
            id: id,
        },
        data: data,
    });
    return response;
});
exports.updateRegularlyUnit = updateRegularlyUnit;
const deleteRegularlyUnit = (id) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.regularly_unit.delete({
        where: {
            id: id,
        },
    });
    return response;
});
exports.deleteRegularlyUnit = deleteRegularlyUnit;
