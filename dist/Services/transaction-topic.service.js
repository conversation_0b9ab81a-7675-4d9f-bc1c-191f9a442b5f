"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateUserAndTransactionTopic = exports.deleteTransactionTopic = exports.updateTransactionTopic = exports.createTransactionTopic = exports.getTransactionTopicDetail = exports.getTransactionTopicList = void 0;
const client_1 = require("@prisma/client");
const time_1 = require("../Utils/time");
const prisma = new client_1.PrismaClient();
const getTransactionTopicList = (params) => __awaiter(void 0, void 0, void 0, function* () {
    const { page, size, search, groupId, userId } = params;
    const response = yield prisma.transaction_topics.findMany({
        skip: (page - 1) * size,
        take: size,
        orderBy: {
            id: 'asc',
        },
        where: {
            name: {
                contains: search,
            },
            transaction_group_id: groupId,
            transaction_group: {
                user_id: userId,
            },
        },
        include: {
            transactions: true,
            transaction_group: true,
        },
    });
    const amount = yield prisma.transaction_topics.count({
        where: {
            name: {
                contains: search,
            },
            transaction_group_id: groupId,
        },
    });
    return { data: response, amount: amount };
});
exports.getTransactionTopicList = getTransactionTopicList;
const getTransactionTopicDetail = (params) => __awaiter(void 0, void 0, void 0, function* () {
    const { userId, id } = params;
    const response = yield prisma.transaction_topics.findFirst({
        where: {
            transaction_group: {
                user_id: userId,
            },
            id: id,
        },
        include: {
            transactions: true,
        },
    });
    return response;
});
exports.getTransactionTopicDetail = getTransactionTopicDetail;
const createTransactionTopic = (data) => __awaiter(void 0, void 0, void 0, function* () {
    data.create_at = (0, time_1.current_datetime)();
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.transaction_topics.create({
        data: data,
    });
    return response;
});
exports.createTransactionTopic = createTransactionTopic;
const updateTransactionTopic = (id, data) => __awaiter(void 0, void 0, void 0, function* () {
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.transaction_topics.update({
        where: {
            id: id,
        },
        data: data,
    });
    return response;
});
exports.updateTransactionTopic = updateTransactionTopic;
const deleteTransactionTopic = (id) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.transaction_topics.delete({
        where: {
            id: id,
        },
    });
    return response;
});
exports.deleteTransactionTopic = deleteTransactionTopic;
const validateUserAndTransactionTopic = (userId, transactionTopicId) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.transaction_topics.findFirst({
        where: {
            id: transactionTopicId,
            transaction_group: {
                user_id: userId,
            },
        },
    });
    return Boolean(response);
});
exports.validateUserAndTransactionTopic = validateUserAndTransactionTopic;
