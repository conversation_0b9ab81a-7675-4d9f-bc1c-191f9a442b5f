"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserByToken = exports.deleteUser = exports.updateUser = exports.getUserDetail = exports.getUsers = void 0;
const client_1 = require("@prisma/client");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const time_1 = require("../Utils/time");
const prisma = new client_1.PrismaClient();
const getUsers = (params) => __awaiter(void 0, void 0, void 0, function* () {
    const { page, size, search } = params;
    const response = yield prisma.users.findMany({
        skip: (page - 1) * size,
        take: size,
        orderBy: {
            id: 'asc',
        },
        where: {
            username: {
                contains: search,
            },
        },
        select: {
            id: true,
            username: true,
            password: false,
            name: true,
            first_name: false,
            lastname_name: false,
            email: true,
            img_url: true,
            create_at: true,
            update_at: true,
            role: {
                select: {
                    id: true,
                    name: true,
                    isSuperAdmin: true,
                    isAdmin: true,
                    isUser: true,
                    isVisitor: true,
                },
            },
        },
    });
    const amount = yield prisma.users.count({
        where: {
            username: {
                contains: search,
            },
        },
    });
    return { users: response, amount: amount };
});
exports.getUsers = getUsers;
const getUserDetail = (id) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.users.findFirst({
        where: {
            id: id,
        },
        select: {
            id: true,
            username: true,
            password: false,
            name: true,
            first_name: false,
            lastname_name: false,
            email: true,
            img_url: true,
            create_at: true,
            update_at: true,
            role: {
                select: {
                    id: true,
                    name: true,
                    isSuperAdmin: true,
                    isAdmin: true,
                    isUser: true,
                    isVisitor: true,
                },
            },
        },
    });
    return response;
});
exports.getUserDetail = getUserDetail;
const updateUser = (id, data) => __awaiter(void 0, void 0, void 0, function* () {
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.users.update({
        where: {
            id: id,
        },
        data: data,
    });
    return response;
});
exports.updateUser = updateUser;
const deleteUser = (id) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.users.delete({
        where: {
            id: id,
        },
    });
    return response;
});
exports.deleteUser = deleteUser;
const getUserByToken = (token) => __awaiter(void 0, void 0, void 0, function* () {
    const tk = String(token).split(' ');
    const decoded = jsonwebtoken_1.default.verify(tk[1], 'moneylabsecret');
    if (decoded) {
        const response = yield prisma.users.findFirst({
            where: {
                id: decoded.user.id,
            },
            select: {
                id: true,
                username: true,
                password: false,
                name: true,
                first_name: false,
                lastname_name: false,
                email: true,
                img_url: true,
                create_at: true,
                update_at: true,
                role: {
                    select: {
                        id: true,
                        name: true,
                        isSuperAdmin: true,
                        isAdmin: true,
                        isUser: true,
                        isVisitor: true,
                    },
                },
            },
        });
        return response;
    }
    else {
        return null;
    }
});
exports.getUserByToken = getUserByToken;
