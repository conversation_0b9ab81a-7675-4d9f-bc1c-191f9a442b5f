"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTotalViewTransactions = exports.getSummary = exports.getYearViewTransactions = exports.getMonthViewTransactions = void 0;
const client_1 = require("@prisma/client");
const moment_timezone_1 = __importDefault(require("moment-timezone"));
const lodash_1 = __importDefault(require("lodash"));
const time_1 = require("../Utils/time");
const prisma = new client_1.PrismaClient();
const getMonthViewTransactions = (params) => __awaiter(void 0, void 0, void 0, function* () {
    const { userId, walletId, topicId, statusId, installmentId, regularlyId, startDate, endDate, transactionType, } = params;
    const dateList = yield prisma.transactions.groupBy({
        by: ['transaction_date_time'],
        orderBy: {
            transaction_date_time: 'asc',
        },
        where: {
            wallet: {
                user_id: userId,
            },
            wallet_id: walletId,
            topic_id: topicId,
            status_id: statusId,
            installment_id: installmentId,
            regularly_id: regularlyId,
            transaction_date_time: {
                lte: endDate,
                gte: startDate,
            },
            transaction_topic: {
                transaction_group: {
                    transaction_type_id: transactionType,
                },
            },
        },
    });
    const typeList = yield prisma.transaction_types.findMany();
    const transactionList = yield prisma.transactions.findMany({
        orderBy: {
            transaction_date_time: 'asc',
        },
        select: {
            id: true,
            name: true,
            description: true,
            current_balance: true,
            amounts: true,
            transaction_date_time: true,
            swap_id: true,
            wallet: {
                select: {
                    id: true,
                    name: true,
                    balance: true,
                    img_url: true,
                    bank: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    wallet_type: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                },
            },
            transaction_topic: {
                select: {
                    id: true,
                    name: true,
                    transaction_group: {
                        select: {
                            id: true,
                            name: true,
                            transaction_type: {
                                select: {
                                    id: true,
                                    name: true,
                                    isSwap: true,
                                },
                            },
                        },
                    },
                },
            },
        },
        where: {
            wallet: {
                user_id: userId,
            },
            wallet_id: walletId,
            topic_id: topicId,
            status_id: statusId,
            installment_id: installmentId,
            regularly_id: regularlyId,
            transaction_date_time: {
                lte: endDate,
                gte: startDate,
            },
            transaction_topic: {
                transaction_group: {
                    transaction_type_id: transactionType,
                },
            },
        },
    });
    const result = [];
    dateList.map((res) => {
        const date_format = (0, moment_timezone_1.default)(res.transaction_date_time).format('YYYY-MM-DD');
        const byTypeList = [];
        typeList.map((type) => {
            const filter = lodash_1.default.filter(transactionList, (o) => {
                return (date_format ===
                    (0, moment_timezone_1.default)(o.transaction_date_time).format('YYYY-MM-DD') &&
                    type.id === o.transaction_topic.transaction_group.transaction_type.id);
            });
            const sum = Number(lodash_1.default.sumBy(filter, (a) => {
                return a.amounts;
            }));
            byTypeList.push({
                transaction_type: type.name,
                transaction_number: filter.length,
                total: type.id === 2 ? sum * -1 : sum,
            });
        });
        const dateResult = lodash_1.default.filter(transactionList, (o) => {
            return (date_format === (0, moment_timezone_1.default)(o.transaction_date_time).format('YYYY-MM-DD'));
        });
        const gResult = lodash_1.default.chain(dateResult)
            .groupBy('transaction_topic.transaction_group.id')
            .map((value, key) => {
            const group = value[0].transaction_topic.transaction_group;
            const type = value[0].transaction_topic.transaction_group.transaction_type;
            const sum = Number(lodash_1.default.sumBy(value, (a) => {
                if (type.isSwap) {
                    if (a.amounts > 0) {
                        return a.amounts;
                    }
                }
                else {
                    return a.amounts;
                }
            }));
            return {
                transaction_group_id: Number(key),
                transaction_group: group.name,
                transaction_type_id: type.id,
                isSwap: type.isSwap,
                transaction_type: type.name,
                transaction_number: value.length,
                total: type.id === 2 ? sum * -1 : sum,
            };
        })
            .value();
        result.push({
            date: date_format,
            count: Number(lodash_1.default.sumBy(gResult, (a) => {
                return a.transaction_number;
            })),
            totalOfDay: Number(lodash_1.default.sumBy(gResult, (a) => {
                if (!a.isSwap) {
                    return a.total;
                }
            })),
            summary: gResult,
        });
    });
    return result;
});
exports.getMonthViewTransactions = getMonthViewTransactions;
const getYearViewTransactions = (params) => __awaiter(void 0, void 0, void 0, function* () {
    const { userId, walletId, topicId, statusId, installmentId, regularlyId, startDate, endDate, transactionType, } = params;
    const dateList = yield prisma.transactions.groupBy({
        by: ['transaction_date_time'],
        orderBy: {
            transaction_date_time: 'asc',
        },
        where: {
            wallet: {
                user_id: userId,
            },
            wallet_id: walletId,
            topic_id: topicId,
            status_id: statusId,
            installment_id: installmentId,
            regularly_id: regularlyId,
            transaction_date_time: {
                lte: endDate,
                gte: startDate,
            },
            transaction_topic: {
                transaction_group: {
                    transaction_type_id: transactionType,
                },
            },
        },
    });
    const months = lodash_1.default.uniqBy(dateList.map((date) => {
        return {
            month_no: (0, moment_timezone_1.default)(date.transaction_date_time).format('MM'),
            month_en: (0, moment_timezone_1.default)(date.transaction_date_time).format('MMMM'),
            month_th: lodash_1.default.find(time_1.MONTHS, (m) => {
                return m.no === (0, moment_timezone_1.default)(date.transaction_date_time).format('MM');
            }).th,
        };
    }), (o) => {
        return o.month_no;
    });
    const transactionList = yield prisma.transactions.findMany({
        orderBy: {
            transaction_date_time: 'asc',
        },
        select: {
            id: true,
            name: true,
            description: true,
            current_balance: true,
            amounts: true,
            transaction_date_time: true,
            wallet: {
                select: {
                    id: true,
                    name: true,
                    balance: true,
                    img_url: true,
                    bank: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    wallet_type: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                },
            },
            transaction_topic: {
                select: {
                    id: true,
                    name: true,
                    transaction_group: {
                        select: {
                            id: true,
                            name: true,
                            transaction_type: {
                                select: {
                                    id: true,
                                    name: true,
                                    isSwap: true,
                                },
                            },
                        },
                    },
                },
            },
        },
        where: {
            wallet: {
                user_id: userId,
            },
            wallet_id: walletId,
            topic_id: topicId,
            status_id: statusId,
            installment_id: installmentId,
            regularly_id: regularlyId,
            transaction_date_time: {
                lte: endDate,
                gte: startDate,
            },
            transaction_topic: {
                transaction_group: {
                    transaction_type_id: transactionType,
                },
            },
        },
    });
    const result = [];
    months.map((res) => {
        const monthResult = lodash_1.default.filter(transactionList, (o) => {
            return res.month_no === (0, moment_timezone_1.default)(o.transaction_date_time).format('MM');
        });
        const gResult = lodash_1.default.chain(monthResult)
            .groupBy('transaction_topic.transaction_group.id')
            .map((value, key) => {
            const group = value[0].transaction_topic.transaction_group;
            const type = value[0].transaction_topic.transaction_group.transaction_type;
            const sum = Number(lodash_1.default.sumBy(value, (a) => {
                if (type.isSwap) {
                    if (a.amounts > 0) {
                        return a.amounts;
                    }
                }
                else {
                    return a.amounts;
                }
            }));
            return {
                transaction_group_id: Number(key),
                transaction_group: group.name,
                transaction_type_id: type.id,
                isSwap: type.isSwap,
                transaction_type: type.name,
                transaction_number: value.length,
                total: type.id === 2 ? sum * -1 : sum,
            };
        })
            .value();
        result.push({
            content: {
                en: {
                    index: 'Month',
                    value: res.month_en,
                },
                th: {
                    index: 'เดือน',
                    value: res.month_th,
                },
            },
            count: Number(lodash_1.default.sumBy(gResult, (a) => {
                return a.transaction_number;
            })),
            total: Number(lodash_1.default.sumBy(gResult, (a) => {
                if (!a.isSwap) {
                    return a.total;
                }
            })),
            summary: gResult,
        });
    });
    return result;
});
exports.getYearViewTransactions = getYearViewTransactions;
const getSummary = (params) => __awaiter(void 0, void 0, void 0, function* () {
    const { userId, walletId, topicId, statusId, installmentId, regularlyId, startDate, endDate, transactionType, } = params;
    const typeList = yield prisma.transaction_types.findMany({
        orderBy: {
            id: 'asc',
        },
        select: {
            id: true,
            name: true,
            isSwap: true,
            transaction_groups: {
                select: {
                    id: true,
                    name: true,
                    transaction_type_id: true,
                    user_id: true,
                    transaction_topics: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                },
            },
        },
    });
    const transactionList = yield prisma.transactions.findMany({
        orderBy: {
            transaction_date_time: 'asc',
        },
        select: {
            id: true,
            name: true,
            description: true,
            current_balance: true,
            amounts: true,
            transaction_date_time: true,
            wallet: {
                select: {
                    id: true,
                    name: true,
                    balance: true,
                    img_url: true,
                    bank: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    wallet_type: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                },
            },
            transaction_topic: {
                select: {
                    id: true,
                    name: true,
                    transaction_group: {
                        select: {
                            id: true,
                            name: true,
                            transaction_type: {
                                select: {
                                    id: true,
                                    name: true,
                                    isSwap: true,
                                },
                            },
                        },
                    },
                },
            },
        },
        where: {
            wallet: {
                user_id: userId,
            },
            wallet_id: walletId,
            topic_id: topicId,
            status_id: statusId,
            installment_id: installmentId,
            regularly_id: regularlyId,
            transaction_date_time: {
                lte: endDate,
                gte: startDate,
            },
            transaction_topic: {
                transaction_group: {
                    transaction_type_id: transactionType,
                },
            },
        },
    });
    const summaryResult = [];
    typeList.map((type) => {
        const filterType = lodash_1.default.filter(transactionList, (o) => {
            return (type.id === o.transaction_topic.transaction_group.transaction_type.id);
        });
        const sum = Number(lodash_1.default.sumBy(filterType, (a) => {
            if (type.isSwap) {
                if (a.amounts > 0) {
                    return a.amounts;
                }
            }
            else {
                return a.amounts;
            }
        }));
        const my_groups = lodash_1.default.filter(type.transaction_groups, (og) => {
            return og.user_id === userId;
        });
        summaryResult.push({
            type_id: type.id,
            transaction_type: type.name,
            transaction_number: filterType.length,
            total: type.id === 2 ? sum * -1 : sum,
            groups: my_groups.map((g) => {
                const filterGroup = lodash_1.default.filter(transactionList, (o) => {
                    return g.id === o.transaction_topic.transaction_group.id;
                });
                const sum_group = Number(lodash_1.default.sumBy(filterGroup, (a) => {
                    if (type.isSwap) {
                        if (a.amounts > 0) {
                            return a.amounts;
                        }
                    }
                    else {
                        return a.amounts;
                    }
                }));
                return {
                    group_id: g.id,
                    transaction_group: g.name,
                    transaction_number: filterGroup.length,
                    total: type.id === 2 ? sum_group * -1 : sum_group,
                    topic: g.transaction_topics.map((t) => {
                        const filterTopic = lodash_1.default.filter(transactionList, (o) => {
                            return t.id === o.transaction_topic.id;
                        });
                        const sum_topic = Number(lodash_1.default.sumBy(filterTopic, (a) => {
                            if (type.isSwap) {
                                if (a.amounts > 0) {
                                    return a.amounts;
                                }
                            }
                            else {
                                return a.amounts;
                            }
                        }));
                        return {
                            topic_id: t.id,
                            transaction_topic: t.name,
                            transaction_number: filterTopic.length,
                            total: type.id === 2 ? sum_topic * -1 : sum_topic,
                        };
                    }),
                };
            }),
        });
    });
    return summaryResult;
});
exports.getSummary = getSummary;
const getTotalViewTransactions = (params) => __awaiter(void 0, void 0, void 0, function* () {
    const { userId, walletId, topicId, statusId, installmentId, regularlyId, transactionType, } = params;
    const dateList = yield prisma.transactions.groupBy({
        by: ['transaction_date_time'],
        orderBy: {
            transaction_date_time: 'asc',
        },
        where: {
            wallet: {
                user_id: userId,
            },
            wallet_id: walletId,
            topic_id: topicId,
            status_id: statusId,
            installment_id: installmentId,
            regularly_id: regularlyId,
            transaction_topic: {
                transaction_group: {
                    transaction_type_id: transactionType,
                },
            },
        },
    });
    const years = lodash_1.default.uniqBy(dateList.map((date) => {
        return {
            year_en: (0, moment_timezone_1.default)(date.transaction_date_time).format('YYYY'),
            year_th: String(Number((0, moment_timezone_1.default)(date.transaction_date_time).format('YYYY')) + 543),
        };
    }), (o) => {
        return o.year_en;
    });
    const transactionList = yield prisma.transactions.findMany({
        orderBy: {
            transaction_date_time: 'asc',
        },
        select: {
            id: true,
            name: true,
            description: true,
            current_balance: true,
            amounts: true,
            transaction_date_time: true,
            wallet: {
                select: {
                    id: true,
                    name: true,
                    balance: true,
                    img_url: true,
                    bank: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                    wallet_type: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                },
            },
            transaction_topic: {
                select: {
                    id: true,
                    name: true,
                    transaction_group: {
                        select: {
                            id: true,
                            name: true,
                            transaction_type: {
                                select: {
                                    id: true,
                                    name: true,
                                    isSwap: true,
                                },
                            },
                        },
                    },
                },
            },
        },
        where: {
            wallet: {
                user_id: userId,
            },
            wallet_id: walletId,
            topic_id: topicId,
            status_id: statusId,
            installment_id: installmentId,
            regularly_id: regularlyId,
            transaction_topic: {
                transaction_group: {
                    transaction_type_id: transactionType,
                },
            },
        },
    });
    const result = [];
    years.map((res) => {
        const yearResult = lodash_1.default.filter(transactionList, (o) => {
            return res.year_en === (0, moment_timezone_1.default)(o.transaction_date_time).format('YYYY');
        });
        const gResult = lodash_1.default.chain(yearResult)
            .groupBy('transaction_topic.transaction_group.id')
            .map((value, key) => {
            const group = value[0].transaction_topic.transaction_group;
            const type = value[0].transaction_topic.transaction_group.transaction_type;
            const sum = Number(lodash_1.default.sumBy(value, (a) => {
                if (type.isSwap) {
                    if (a.amounts > 0) {
                        return a.amounts;
                    }
                }
                else {
                    return a.amounts;
                }
            }));
            return {
                transaction_group_id: Number(key),
                transaction_group: group.name,
                transaction_type_id: type.id,
                isSwap: type.isSwap,
                transaction_type: type.name,
                transaction_number: value.length,
                total: type.id === 2 ? sum * -1 : sum,
            };
        })
            .value();
        result.push({
            content: {
                en: {
                    index: 'Year',
                    value: res.year_en,
                },
                th: {
                    index: 'ปี',
                    value: res.year_th,
                },
            },
            count: Number(lodash_1.default.sumBy(gResult, (a) => {
                return a.transaction_number;
            })),
            total: Number(lodash_1.default.sumBy(gResult, (a) => {
                if (!a.isSwap) {
                    return a.total;
                }
            })),
            summary: gResult,
        });
    });
    return result;
});
exports.getTotalViewTransactions = getTotalViewTransactions;
