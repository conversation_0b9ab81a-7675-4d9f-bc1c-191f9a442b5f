"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateUserAndWallet = exports.deleteWallet = exports.updateWallet = exports.createWallet = exports.getWalletDetail = exports.getWallets = void 0;
const client_1 = require("@prisma/client");
const time_1 = require("../Utils/time");
const prisma = new client_1.PrismaClient();
const getWallets = (params) => __awaiter(void 0, void 0, void 0, function* () {
    const { page, size, search, userId, bankId, typeId } = params;
    const response = yield prisma.wallets.findMany({
        skip: (page - 1) * size,
        take: size,
        orderBy: {
            id: 'asc',
        },
        where: {
            name: {
                contains: search,
            },
            user_id: userId,
            bank_id: bankId,
            wallet_type_id: typeId,
        },
        include: {
            transactions: false,
            bank: true,
            wallet_type: true,
        },
    });
    const amount = yield prisma.wallets.count({
        where: {
            name: {
                contains: search,
            },
            user_id: userId,
            bank_id: bankId,
            wallet_type_id: typeId,
        },
    });
    return { data: response, amount: amount };
});
exports.getWallets = getWallets;
const getWalletDetail = (params) => __awaiter(void 0, void 0, void 0, function* () {
    const { userId, id } = params;
    const response = yield prisma.wallets.findFirst({
        where: {
            user_id: userId,
            id: id,
        },
        include: {
            transactions: true,
        },
    });
    return response;
});
exports.getWalletDetail = getWalletDetail;
const createWallet = (data) => __awaiter(void 0, void 0, void 0, function* () {
    data.create_at = (0, time_1.current_datetime)();
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.wallets.create({
        data: data,
    });
    return response;
});
exports.createWallet = createWallet;
const updateWallet = (id, data) => __awaiter(void 0, void 0, void 0, function* () {
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.wallets.update({
        where: {
            id: id,
        },
        data: data,
    });
    return response;
});
exports.updateWallet = updateWallet;
const deleteWallet = (id) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.wallets.delete({
        where: {
            id: id,
        },
    });
    return response;
});
exports.deleteWallet = deleteWallet;
const validateUserAndWallet = (userId, walletId) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.wallets.findFirst({
        where: {
            id: walletId,
            user_id: userId,
        },
    });
    return Boolean(response);
});
exports.validateUserAndWallet = validateUserAndWallet;
