"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateUserAndInstallment = exports.deleteInstallment = exports.updateInstallment = exports.createInstallment = exports.getInstallmentDetail = exports.getInstallmentList = void 0;
const client_1 = require("@prisma/client");
const time_1 = require("../Utils/time");
const prisma = new client_1.PrismaClient();
const getInstallmentList = (params) => __awaiter(void 0, void 0, void 0, function* () {
    const { page, size, search, topicId } = params;
    const response = yield prisma.installments.findMany({
        skip: (page - 1) * size,
        take: size,
        orderBy: {
            id: 'asc',
        },
        where: {
            name: {
                contains: search,
            },
            topic_id: topicId,
        },
        include: {
            transactions: true,
            transaction_topic: true,
        },
    });
    const amount = yield prisma.installments.count({
        where: {
            name: {
                contains: search,
            },
            topic_id: topicId,
        },
    });
    return { data: response, amount: amount };
});
exports.getInstallmentList = getInstallmentList;
const getInstallmentDetail = (params) => __awaiter(void 0, void 0, void 0, function* () {
    const { userId, id } = params;
    const response = yield prisma.installments.findFirst({
        where: {
            transaction_topic: {
                transaction_group: {
                    user_id: userId,
                },
            },
            id: id,
        },
        include: {
            transactions: true,
        },
    });
    return response;
});
exports.getInstallmentDetail = getInstallmentDetail;
const createInstallment = (data) => __awaiter(void 0, void 0, void 0, function* () {
    data.create_at = (0, time_1.current_datetime)();
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.installments.create({
        data: data,
    });
    return response;
});
exports.createInstallment = createInstallment;
const updateInstallment = (id, data) => __awaiter(void 0, void 0, void 0, function* () {
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.installments.update({
        where: {
            id: id,
        },
        data: data,
    });
    return response;
});
exports.updateInstallment = updateInstallment;
const deleteInstallment = (id) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.installments.delete({
        where: {
            id: id,
        },
    });
    return response;
});
exports.deleteInstallment = deleteInstallment;
const validateUserAndInstallment = (userId, installmentId) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.installments.findFirst({
        where: {
            id: installmentId,
            transaction_topic: {
                transaction_group: {
                    user_id: userId,
                },
            },
        },
    });
    return Boolean(response);
});
exports.validateUserAndInstallment = validateUserAndInstallment;
