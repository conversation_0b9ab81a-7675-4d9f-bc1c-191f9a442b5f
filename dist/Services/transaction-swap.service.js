"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteTransactionSwap = exports.updateTransactionGroup = exports.createTransactionSwap = exports.getTransactionSwaps = void 0;
const client_1 = require("@prisma/client");
const time_1 = require("../Utils/time");
const lodash_1 = __importDefault(require("lodash"));
const transaction_service_1 = require("./transaction.service");
const prisma = new client_1.PrismaClient();
const getTransactionSwaps = (params) => __awaiter(void 0, void 0, void 0, function* () {
    const { page, size, search, userId, startDate, endDate } = params;
    const response = yield prisma.transaction_swaps.findMany({
        skip: (page - 1) * size,
        take: size,
        orderBy: {
            create_at: 'desc',
        },
        where: {
            name: {
                contains: search,
            },
            user_id: userId,
            create_at: {
                lte: endDate,
                gte: startDate,
            },
        },
        include: {
            transactions: true,
        },
    });
    const amount = yield prisma.transaction_swaps.count({
        where: {
            name: {
                contains: search,
            },
            user_id: userId,
            create_at: {
                lte: endDate,
                gte: startDate,
            },
        },
    });
    return { data: response, amount: amount };
});
exports.getTransactionSwaps = getTransactionSwaps;
const createTransactionSwap = (data) => __awaiter(void 0, void 0, void 0, function* () {
    data.create_at = (0, time_1.current_datetime)();
    data.update_at = (0, time_1.current_datetime)();
    const swap_data = lodash_1.default.omit(data, ['transactions']);
    const response = yield prisma.transaction_swaps
        .create({
        data: swap_data,
    })
        .then((res) => {
        if (data.transactions.length > 0) {
            data.transactions.map((transaction) => __awaiter(void 0, void 0, void 0, function* () {
                transaction.swap_id = res.id;
                yield (0, transaction_service_1.createTransaction)(transaction);
            }));
        }
        return res;
    });
    return response;
});
exports.createTransactionSwap = createTransactionSwap;
const updateTransactionGroup = (id, data) => __awaiter(void 0, void 0, void 0, function* () {
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.transaction_groups.update({
        where: {
            id: id,
        },
        data: data,
    });
    return response;
});
exports.updateTransactionGroup = updateTransactionGroup;
const deleteTransactionSwap = (id) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.transaction_swaps.delete({
        where: {
            id: id,
        },
    });
    return response;
});
exports.deleteTransactionSwap = deleteTransactionSwap;
