"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteBank = exports.updateBank = exports.createBank = exports.getBanks = void 0;
const client_1 = require("@prisma/client");
const time_1 = require("../Utils/time");
const prisma = new client_1.PrismaClient();
const getBanks = () => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.banks.findMany({
        where: {
            NOT: {
                id: 0,
            },
        },
        orderBy: {
            id: 'asc',
        },
    });
    return response;
});
exports.getBanks = getBanks;
const createBank = (data) => __awaiter(void 0, void 0, void 0, function* () {
    data.create_at = (0, time_1.current_datetime)();
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.banks.create({
        data: data,
    });
    return response;
});
exports.createBank = createBank;
const updateBank = (id, data) => __awaiter(void 0, void 0, void 0, function* () {
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.banks.update({
        where: {
            id: id,
        },
        data: data,
    });
    return response;
});
exports.updateBank = updateBank;
const deleteBank = (id) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.banks.delete({
        where: {
            id: id,
        },
    });
    return response;
});
exports.deleteBank = deleteBank;
