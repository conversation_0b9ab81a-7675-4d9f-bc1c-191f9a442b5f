"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteWalletType = exports.updateWalletType = exports.createWalletType = exports.getWalletTypes = void 0;
const client_1 = require("@prisma/client");
const time_1 = require("../Utils/time");
const prisma = new client_1.PrismaClient();
const getWalletTypes = () => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.wallet_types.findMany({
        include: {
            wallets: {
                select: {
                    id: true,
                    name: true,
                },
            },
        },
    });
    return response;
});
exports.getWalletTypes = getWalletTypes;
const createWalletType = (data) => __awaiter(void 0, void 0, void 0, function* () {
    data.create_at = (0, time_1.current_datetime)();
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.wallet_types.create({
        data: data,
    });
    return response;
});
exports.createWalletType = createWalletType;
const updateWalletType = (id, data) => __awaiter(void 0, void 0, void 0, function* () {
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.wallet_types.update({
        where: {
            id: id,
        },
        data: data,
    });
    return response;
});
exports.updateWalletType = updateWalletType;
const deleteWalletType = (id) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.wallet_types.delete({
        where: {
            id: id,
        },
    });
    return response;
});
exports.deleteWalletType = deleteWalletType;
