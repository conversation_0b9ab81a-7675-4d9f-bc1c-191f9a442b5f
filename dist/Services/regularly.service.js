"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateUserAndRegularly = exports.deleteRegularly = exports.updateRegularly = exports.createRegularly = exports.getRegularlyDetail = exports.getRegularlyList = void 0;
const client_1 = require("@prisma/client");
const time_1 = require("../Utils/time");
const prisma = new client_1.PrismaClient();
const getRegularlyList = (params) => __awaiter(void 0, void 0, void 0, function* () {
    const { page, size, search, userId, topicId, unitId, typeId } = params;
    const response = yield prisma.regularly.findMany({
        skip: (page - 1) * size,
        take: size,
        orderBy: {
            id: 'asc',
        },
        where: {
            name: {
                contains: search,
            },
            transaction_topic: {
                transaction_group: {
                    user_id: userId,
                },
            },
            topic_id: topicId,
            unit_id: unitId,
            type_id: typeId,
        },
        include: {
            transactions: true,
            type: true,
            unit: true,
            transaction_topic: true,
        },
    });
    const amount = yield prisma.regularly.count({
        where: {
            name: {
                contains: search,
            },
            transaction_topic: {
                transaction_group: {
                    user_id: userId,
                },
            },
            topic_id: topicId,
            unit_id: unitId,
            type_id: typeId,
        },
    });
    return { data: response, amount: amount };
});
exports.getRegularlyList = getRegularlyList;
const getRegularlyDetail = (params) => __awaiter(void 0, void 0, void 0, function* () {
    const { userId, id } = params;
    const response = yield prisma.regularly.findFirst({
        where: {
            id: id,
            transaction_topic: {
                transaction_group: {
                    user_id: userId,
                },
            },
        },
        include: {
            transactions: true,
            type: true,
            unit: true,
            transaction_topic: true,
        },
    });
    return response;
});
exports.getRegularlyDetail = getRegularlyDetail;
const createRegularly = (data) => __awaiter(void 0, void 0, void 0, function* () {
    data.create_at = (0, time_1.current_datetime)();
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.regularly.create({
        data: data,
    });
    return response;
});
exports.createRegularly = createRegularly;
const updateRegularly = (id, data) => __awaiter(void 0, void 0, void 0, function* () {
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.regularly.update({
        where: {
            id: id,
        },
        data: data,
    });
    return response;
});
exports.updateRegularly = updateRegularly;
const deleteRegularly = (id) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.regularly.delete({
        where: {
            id: id,
        },
    });
    return response;
});
exports.deleteRegularly = deleteRegularly;
const validateUserAndRegularly = (userId, regularlyId) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.regularly.findFirst({
        where: {
            id: regularlyId,
            transaction_topic: {
                transaction_group: {
                    user_id: userId,
                },
            },
        },
    });
    return Boolean(response);
});
exports.validateUserAndRegularly = validateUserAndRegularly;
