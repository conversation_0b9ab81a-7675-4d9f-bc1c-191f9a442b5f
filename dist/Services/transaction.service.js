"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.importTransaction = exports.validateUserAndTransaction = exports.deleteTransaction = exports.updateTransaction = exports.createTransaction = exports.getTransactionDetail = exports.getTransactionList = void 0;
const client_1 = require("@prisma/client");
const time_1 = require("../Utils/time");
const prisma = new client_1.PrismaClient();
const getTransactionList = (params) => __awaiter(void 0, void 0, void 0, function* () {
    const { page, size, search, userId, walletId, topicId, statusId, installmentId, regularlyId, startDate, endDate, } = params;
    const response = yield prisma.transactions.findMany({
        skip: (page - 1) * size,
        take: size,
        orderBy: {
            transaction_date_time: 'asc',
        },
        where: {
            name: {
                contains: search,
            },
            wallet: {
                user_id: userId,
            },
            wallet_id: walletId,
            topic_id: topicId,
            status_id: statusId,
            installment_id: installmentId,
            regularly_id: regularlyId,
            transaction_date_time: {
                lte: endDate,
                gte: startDate,
            },
        },
        include: {
            wallet: true,
            transaction_topic: {
                include: {
                    transaction_group: {
                        include: {
                            transaction_type: true,
                        },
                    },
                },
            },
            transaction_status: true,
            installment: true,
            regularly: true,
            transaction_swap: true,
        },
    });
    const amount = yield prisma.transactions.count({
        where: {
            name: {
                contains: search,
            },
            wallet: {
                user_id: userId,
            },
            wallet_id: walletId,
            topic_id: topicId,
            status_id: statusId,
            installment_id: installmentId,
            regularly_id: regularlyId,
        },
    });
    return { data: response, amount: amount };
});
exports.getTransactionList = getTransactionList;
const getTransactionDetail = (params) => __awaiter(void 0, void 0, void 0, function* () {
    const { userId, id } = params;
    const response = yield prisma.transactions.findFirst({
        where: {
            id: id,
            wallet: {
                user_id: userId,
            },
        },
        include: {
            wallet: true,
            transaction_topic: true,
            transaction_status: true,
            installment: true,
            regularly: true,
        },
    });
    return response;
});
exports.getTransactionDetail = getTransactionDetail;
const createTransaction = (data) => __awaiter(void 0, void 0, void 0, function* () {
    data.create_at = (0, time_1.current_datetime)();
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.transactions.create({
        data: data,
    });
    return response;
});
exports.createTransaction = createTransaction;
const updateTransaction = (id, data) => __awaiter(void 0, void 0, void 0, function* () {
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.transactions.update({
        where: {
            id: id,
        },
        data: data,
    });
    return response;
});
exports.updateTransaction = updateTransaction;
const deleteTransaction = (id) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.transactions.delete({
        where: {
            id: id,
        },
    });
    return response;
});
exports.deleteTransaction = deleteTransaction;
const validateUserAndTransaction = (userId, transactionId) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.transactions.findFirst({
        where: {
            id: transactionId,
            wallet: {
                user_id: userId,
            },
        },
    });
    return Boolean(response);
});
exports.validateUserAndTransaction = validateUserAndTransaction;
const importTransaction = (data) => __awaiter(void 0, void 0, void 0, function* () {
    console.log(data);
    const response = yield prisma.transactions.createMany({
        data: data,
    });
    return response;
});
exports.importTransaction = importTransaction;
