"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.importGroups = exports.validateUserAndTransactionGroup = exports.deleteTransactionGroup = exports.updateTransactionGroup = exports.createTransactionGroup = exports.getTransactionGroupDetail = exports.getTransactionGroupList = void 0;
const client_1 = require("@prisma/client");
const time_1 = require("../Utils/time");
const prisma = new client_1.PrismaClient();
const getTransactionGroupList = (params) => __awaiter(void 0, void 0, void 0, function* () {
    const { page, size, search, userId, typeId } = params;
    const response = yield prisma.transaction_groups.findMany({
        skip: (page - 1) * size,
        take: size,
        orderBy: {
            id: 'asc',
        },
        where: {
            name: {
                contains: search,
            },
            user_id: userId,
            transaction_type_id: typeId,
        },
        include: {
            transaction_topics: true,
            transaction_type: true,
        },
    });
    const amount = yield prisma.transaction_groups.count({
        where: {
            name: {
                contains: search,
            },
            user_id: userId,
            transaction_type_id: typeId,
        },
    });
    return { data: response, amount: amount };
});
exports.getTransactionGroupList = getTransactionGroupList;
const getTransactionGroupDetail = (params) => __awaiter(void 0, void 0, void 0, function* () {
    const { userId, id } = params;
    const response = yield prisma.transaction_groups.findFirst({
        where: {
            user_id: userId,
            id: id,
        },
        include: {
            transaction_topics: true,
        },
    });
    return response;
});
exports.getTransactionGroupDetail = getTransactionGroupDetail;
const createTransactionGroup = (data) => __awaiter(void 0, void 0, void 0, function* () {
    data.create_at = (0, time_1.current_datetime)();
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.transaction_groups.create({
        data: data,
    });
    return response;
});
exports.createTransactionGroup = createTransactionGroup;
const updateTransactionGroup = (id, data) => __awaiter(void 0, void 0, void 0, function* () {
    data.update_at = (0, time_1.current_datetime)();
    const response = yield prisma.transaction_groups.update({
        where: {
            id: id,
        },
        data: data,
    });
    return response;
});
exports.updateTransactionGroup = updateTransactionGroup;
const deleteTransactionGroup = (id) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.transaction_groups.delete({
        where: {
            id: id,
        },
    });
    return response;
});
exports.deleteTransactionGroup = deleteTransactionGroup;
const validateUserAndTransactionGroup = (userId, transactionGroupId) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.transaction_groups.findFirst({
        where: {
            id: transactionGroupId,
            user_id: userId,
        },
    });
    return Boolean(response);
});
exports.validateUserAndTransactionGroup = validateUserAndTransactionGroup;
const importGroups = (userId) => __awaiter(void 0, void 0, void 0, function* () {
    const response = yield prisma.transaction_groups.findMany({
        orderBy: {
            id: 'asc',
        },
        where: {
            user_id: 2,
        },
        select: {
            name: true,
            transaction_type_id: true,
            transaction_topics: true,
        },
    });
    if (response) {
        response.map((g) => __awaiter(void 0, void 0, void 0, function* () {
            g.user_id = userId;
            g.create_at = (0, time_1.current_datetime)();
            g.update_at = (0, time_1.current_datetime)();
            const group_payload = {
                user_id: userId,
                name: g.name,
                transaction_type_id: g.transaction_type_id,
                create_at: (0, time_1.current_datetime)(),
                update_at: (0, time_1.current_datetime)(),
            };
            const res = yield prisma.transaction_groups.create({
                data: group_payload,
            });
            if (g.transaction_topics) {
                yield g.transaction_topics.map((t) => __awaiter(void 0, void 0, void 0, function* () {
                    const topic_payload = {
                        name: t.name,
                        transaction_group_id: res.id,
                        create_at: (0, time_1.current_datetime)(),
                        update_at: (0, time_1.current_datetime)(),
                    };
                    yield prisma.transaction_topics.create({
                        data: topic_payload,
                    });
                }));
            }
        }));
    }
    return response;
});
exports.importGroups = importGroups;
