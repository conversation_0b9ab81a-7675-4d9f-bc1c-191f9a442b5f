"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.objectResponse = exports.paginationResponse = exports.errorUndifined = exports.errorResponse = void 0;
const errorResponse = (err, message) => {
    return {
        status: false,
        code: 500,
        message: message,
        err: err,
    };
};
exports.errorResponse = errorResponse;
const errorUndifined = (err, message) => {
    return {
        status: false,
        code: 401,
        message: message,
        err: err,
    };
};
exports.errorUndifined = errorUndifined;
const paginationResponse = (page, size, total, data) => {
    return {
        status: true,
        message: 'Success',
        code: 200,
        isPagination: true,
        data: {
            current_page: Number(page) || 1,
            pages: Math.ceil(Number(total) / Number(size)),
            size: Number(size) || 10,
            total: Number(total) || 0,
            content: data || [],
        },
    };
};
exports.paginationResponse = paginationResponse;
const objectResponse = (data) => {
    return {
        status: true,
        message: 'Success',
        code: 200,
        isPagination: false,
        data: data || {},
    };
};
exports.objectResponse = objectResponse;
