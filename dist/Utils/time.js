"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MONTHS = exports.current_datetime = void 0;
const moment_timezone_1 = __importDefault(require("moment-timezone"));
const current_datetime = () => {
    return moment_timezone_1.default.tz('Asia/Bangkok').add(7, 'hours');
};
exports.current_datetime = current_datetime;
exports.MONTHS = [
    {
        no: '01',
        en: 'January',
        th: 'มาราคม',
    },
    {
        no: '02',
        en: 'February',
        th: 'กุมภาพันธ์',
    },
    {
        no: '03',
        en: 'March',
        th: 'มีนาคม',
    },
    {
        no: '04',
        en: 'April',
        th: 'เมษายน',
    },
    {
        no: '05',
        en: 'May',
        th: 'พฤษภาคม',
    },
    {
        no: '06',
        en: 'June',
        th: 'มิถุนายน',
    },
    {
        no: '07',
        en: 'July',
        th: 'กรกฎาคม',
    },
    {
        no: '08',
        en: 'August',
        th: 'สิงหาคม',
    },
    {
        no: '09',
        en: 'September',
        th: 'กันยายน',
    },
    {
        no: '10',
        en: 'October',
        th: 'ตุลาคม',
    },
    {
        no: '11',
        en: 'November',
        th: 'พฤศจิกายน',
    },
    {
        no: '12',
        en: 'December',
        th: 'ธันวาคม',
    },
];
