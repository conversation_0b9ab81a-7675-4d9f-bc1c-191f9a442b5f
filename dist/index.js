"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const cors_1 = __importDefault(require("cors"));
const express_1 = __importDefault(require("express"));
const morgan_1 = __importDefault(require("morgan"));
const body_parser_1 = __importDefault(require("body-parser"));
const fs_1 = require("fs");
const app = (0, express_1.default)();
app.use((0, morgan_1.default)('dev'));
app.use((0, cors_1.default)());
app.use(body_parser_1.default.json({ limit: '10mb' }));
(0, fs_1.readdirSync)(process.env.ROUTES_PATH || './src/Routes').map((r) => {
    const split = r.split('.');
    const route_name = `${split[0]}.${split[1]}`;
    app.use('/api', require(`./Routes/${route_name}`));
});
const PORT = process.env.PORT || 4000;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
