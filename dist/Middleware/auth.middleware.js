"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const response_1 = require("../Utils/response");
const user_service_1 = require("../Services/user.service");
exports.auth = (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const token = req.headers.authorization;
        if (!token) {
            res.status(401).send((0, response_1.errorUndifined)(null, 'No Token'));
        }
        else {
            const user = yield (0, user_service_1.getUserByToken)(String(token));
            if (user) {
                next();
            }
            else {
                res.status(401).send((0, response_1.errorUndifined)(null, 'No Invalid'));
            }
        }
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Token Invalid'));
    }
});
