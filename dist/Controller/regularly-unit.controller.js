"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const response_1 = require("../Utils/response");
const regularly_unit_service_1 = require("../Services/regularly-unit.service");
exports.List = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const response = yield (0, regularly_unit_service_1.getRegularlyUnitList)();
        res.status(200).json((0, response_1.objectResponse)(response));
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
exports.Create = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const response = yield (0, regularly_unit_service_1.createRegularlyUnit)(req.body);
        res.status(200).json((0, response_1.objectResponse)(response));
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
exports.Update = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const response = yield (0, regularly_unit_service_1.updateRegularlyUnit)(Number(id), req.body);
        res.status(200).json((0, response_1.objectResponse)(response));
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
exports.Remove = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const response = yield (0, regularly_unit_service_1.deleteRegularlyUnit)(Number(id));
        res.status(200).json((0, response_1.objectResponse)(response));
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
