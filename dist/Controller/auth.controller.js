"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const auth_service_1 = require("../Services/auth.service");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const user_service_1 = require("../Services/user.service");
const response_1 = require("../Utils/response");
const transaction_group_service_1 = require("../Services/transaction-group.service");
exports.register = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { username, password } = req.body;
        const user = yield (0, auth_service_1.checkUser)(username);
        if (user) {
            res.status(400).json({ message: 'User Already Exists!!!' });
        }
        else {
            const salt = yield bcryptjs_1.default.genSalt(10);
            const newUser = req.body;
            newUser.password = yield bcryptjs_1.default.hash(password, salt);
            const createUser = yield (0, auth_service_1.register)(newUser);
            if (createUser) {
                const import_group = yield (0, transaction_group_service_1.importGroups)(createUser.id);
                console.log(import_group);
            }
            res.status(200).json({ message: 'Register Success!!', data: createUser });
        }
    }
    catch (err) {
        console.log(err);
        res.status(500).send('Server Error');
    }
});
exports.login = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { username, password } = req.body;
        const user = yield (0, auth_service_1.checkUser)(username);
        if (user) {
            const isMatch = yield bcryptjs_1.default.compare(password, user.password);
            if (!isMatch) {
                res.status(400).json({ message: 'Password Invalid!!!' });
            }
            const payload = {
                user: {
                    id: user.id,
                    username: user.username,
                },
            };
            jsonwebtoken_1.default.sign(payload, 'moneylabsecret', (err, token) => {
                if (err)
                    throw err;
                res.status(200).json({ message: 'Login Success!!', token: token });
            });
        }
        else {
            res.status(400).json({ message: 'Username Invalid!!!' });
        }
    }
    catch (err) {
        console.log(err);
        res.status(500).send('Server Error');
    }
});
exports.getMe = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const token = req.headers.authorization;
        if (token) {
            const user = yield (0, user_service_1.getUserByToken)(String(token));
            if (user) {
                const response = yield (0, user_service_1.getUserDetail)(user.id);
                if (response) {
                    res.status(200).json((0, response_1.objectResponse)(response));
                }
                else {
                    res.status(401).json((0, response_1.errorUndifined)(null, 'user undefined'));
                }
            }
        }
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
