"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const response_1 = require("../Utils/response");
const user_service_1 = require("../Services/user.service");
const transaction_service_1 = require("../Services/transaction.service");
const import_1 = require("../Utils/import");
const lodash_1 = __importDefault(require("lodash"));
const validate_1 = require("../Utils/validate");
exports.List = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const token = req.headers.authorization;
        const { page, size, search, walletId, topicId, statusId, installmentId, regularlyId, userId, startDate, endDate, } = req.query;
        if (token) {
            const user = yield (0, user_service_1.getUserByToken)(String(token));
            if (user) {
                const response = yield (0, transaction_service_1.getTransactionList)({
                    page: Number(page) || 1,
                    size: Number(size) || 20,
                    search: search === undefined || search === null ? '' : String(search),
                    userId: (0, validate_1.isSuperAdminParam)(user, userId),
                    topicId: Number(topicId) || undefined,
                    walletId: Number(walletId) || undefined,
                    statusId: Number(statusId) || undefined,
                    installmentId: Number(installmentId) || undefined,
                    regularlyId: Number(regularlyId) || undefined,
                    startDate: startDate === undefined ? undefined : new Date(String(startDate)),
                    endDate: endDate === undefined ? undefined : new Date(String(endDate)),
                });
                res
                    .status(200)
                    .json((0, response_1.paginationResponse)(page || 1, size || 20, response.amount, response.data));
            }
        }
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
exports.Detail = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const token = req.headers.authorization;
        const { id } = req.params;
        if (token) {
            const user = yield (0, user_service_1.getUserByToken)(String(token));
            if (user) {
                const response = yield (0, transaction_service_1.getTransactionDetail)({
                    id: Number(id),
                    userId: user.id,
                });
                res.status(200).json((0, response_1.objectResponse)(response));
            }
        }
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
exports.Create = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const token = req.headers.authorization;
        if (token) {
            const user = yield (0, user_service_1.getUserByToken)(String(token));
            if (user) {
                const data = yield req.body;
                const response = yield (0, transaction_service_1.createTransaction)(data);
                res.status(200).json((0, response_1.objectResponse)(response));
            }
        }
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
exports.Update = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const token = req.headers.authorization;
        if (token) {
            const user = yield (0, user_service_1.getUserByToken)(String(token));
            if (user) {
                const validate = yield (0, transaction_service_1.validateUserAndTransaction)(Number(user.id), Number(id));
                if (validate) {
                    const response = yield (0, transaction_service_1.updateTransaction)(Number(id), req.body);
                    res.status(200).json((0, response_1.objectResponse)(response));
                }
            }
        }
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
exports.Remove = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const token = req.headers.authorization;
        if (token) {
            const user = yield (0, user_service_1.getUserByToken)(String(token));
            if (user) {
                const validate = yield (0, transaction_service_1.validateUserAndTransaction)(Number(user.id), Number(id));
                if (validate) {
                    const response = yield (0, transaction_service_1.deleteTransaction)(Number(id));
                    res.status(200).json((0, response_1.objectResponse)(response));
                }
            }
        }
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
exports.Import = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const data = yield (0, import_1.generate_array)();
        const array_filter = yield lodash_1.default.filter(data.array, (o) => {
            return o.amounts > 0;
        });
        res
            .status(200)
            .json({ count: array_filter.length, raw: data.raw, data: array_filter });
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
