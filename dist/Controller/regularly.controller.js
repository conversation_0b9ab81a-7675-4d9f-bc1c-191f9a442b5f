"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const response_1 = require("../Utils/response");
const user_service_1 = require("../Services/user.service");
const regularly_service_1 = require("../Services/regularly.service");
exports.List = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const token = req.headers.authorization;
        const { page, size, search, topicId, unitId, typeId } = req.query;
        if (token) {
            const user = yield (0, user_service_1.getUserByToken)(String(token));
            if (user) {
                const response = yield (0, regularly_service_1.getRegularlyList)({
                    page: Number(page) || 1,
                    size: Number(size) || 20,
                    search: search === undefined || search === null ? '' : String(search),
                    userId: user.id,
                    topicId: Number(topicId) || undefined,
                    typeId: Number(typeId) || undefined,
                    unitId: Number(unitId) || undefined,
                });
                res
                    .status(200)
                    .json((0, response_1.paginationResponse)(page || 1, size || 20, response.amount, response.data));
            }
        }
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
exports.Detail = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const token = req.headers.authorization;
        const { id } = req.params;
        if (token) {
            const user = yield (0, user_service_1.getUserByToken)(String(token));
            if (user) {
                const response = yield (0, regularly_service_1.getRegularlyDetail)({
                    id: Number(id),
                    userId: user.id,
                });
                res.status(200).json((0, response_1.objectResponse)(response));
            }
        }
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
exports.Create = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const token = req.headers.authorization;
        if (token) {
            const user = yield (0, user_service_1.getUserByToken)(String(token));
            if (user) {
                const data = yield req.body;
                const response = yield (0, regularly_service_1.createRegularly)(data);
                res.status(200).json((0, response_1.objectResponse)(response));
            }
        }
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
exports.Update = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const token = req.headers.authorization;
        if (token) {
            const user = yield (0, user_service_1.getUserByToken)(String(token));
            if (user) {
                const validate = yield (0, regularly_service_1.validateUserAndRegularly)(Number(user.id), Number(id));
                if (validate) {
                    const response = yield (0, regularly_service_1.updateRegularly)(Number(id), req.body);
                    res.status(200).json((0, response_1.objectResponse)(response));
                }
            }
        }
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
exports.Remove = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { id } = req.params;
        const token = req.headers.authorization;
        if (token) {
            const user = yield (0, user_service_1.getUserByToken)(String(token));
            if (user) {
                const validate = yield (0, regularly_service_1.validateUserAndRegularly)(Number(user.id), Number(id));
                if (validate) {
                    const response = yield (0, regularly_service_1.deleteRegularly)(Number(id));
                    res.status(200).json((0, response_1.objectResponse)(response));
                }
            }
        }
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
