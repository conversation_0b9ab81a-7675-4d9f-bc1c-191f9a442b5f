"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const response_1 = require("../Utils/response");
const user_service_1 = require("../Services/user.service");
const schedule_service_1 = require("../Services/schedule.service");
const validate_1 = require("../Utils/validate");
exports.MonthViewList = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const token = req.headers.authorization;
        const { walletId, topicId, statusId, installmentId, regularlyId, userId, startDate, endDate, transactionType, } = req.query;
        if (token) {
            const user = yield (0, user_service_1.getUserByToken)(String(token));
            if (user) {
                const response = yield (0, schedule_service_1.getMonthViewTransactions)({
                    userId: (0, validate_1.isSuperAdminParam)(user, userId),
                    topicId: Number(topicId) || undefined,
                    walletId: Number(walletId) || undefined,
                    statusId: Number(statusId) || undefined,
                    installmentId: Number(installmentId) || undefined,
                    regularlyId: Number(regularlyId) || undefined,
                    transactionType: Number(transactionType) || undefined,
                    startDate: startDate === undefined ? undefined : new Date(String(startDate)),
                    endDate: endDate === undefined ? undefined : new Date(String(endDate)),
                });
                res.status(200).json((0, response_1.objectResponse)(response));
            }
        }
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
exports.YearViewList = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const token = req.headers.authorization;
        const { walletId, topicId, statusId, installmentId, regularlyId, userId, year, transactionType, } = req.query;
        if (token) {
            const user = yield (0, user_service_1.getUserByToken)(String(token));
            if (user) {
                const response = yield (0, schedule_service_1.getYearViewTransactions)({
                    userId: (0, validate_1.isSuperAdminParam)(user, userId),
                    topicId: Number(topicId) || undefined,
                    walletId: Number(walletId) || undefined,
                    statusId: Number(statusId) || undefined,
                    installmentId: Number(installmentId) || undefined,
                    regularlyId: Number(regularlyId) || undefined,
                    transactionType: Number(transactionType) || undefined,
                    startDate: year === undefined ? undefined : new Date(String(`${year}-01-01`)),
                    endDate: year === undefined ? undefined : new Date(String(`${year}-12-31`)),
                });
                res.status(200).json((0, response_1.objectResponse)(response));
            }
        }
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
exports.Summary = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const token = req.headers.authorization;
        const { walletId, topicId, statusId, installmentId, regularlyId, userId, startDate, endDate, transactionType, } = req.query;
        if (token) {
            const user = yield (0, user_service_1.getUserByToken)(String(token));
            if (user) {
                const response = yield (0, schedule_service_1.getSummary)({
                    userId: (0, validate_1.isSuperAdminParam)(user, userId),
                    topicId: Number(topicId) || undefined,
                    walletId: Number(walletId) || undefined,
                    statusId: Number(statusId) || undefined,
                    installmentId: Number(installmentId) || undefined,
                    regularlyId: Number(regularlyId) || undefined,
                    transactionType: Number(transactionType) || undefined,
                    startDate: startDate === undefined ? undefined : new Date(String(startDate)),
                    endDate: endDate === undefined ? undefined : new Date(String(endDate)),
                });
                res.status(200).json((0, response_1.objectResponse)(response));
            }
        }
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
exports.TotalViewList = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const token = req.headers.authorization;
        const { walletId, topicId, statusId, installmentId, regularlyId, userId, transactionType, } = req.query;
        if (token) {
            const user = yield (0, user_service_1.getUserByToken)(String(token));
            if (user) {
                const response = yield (0, schedule_service_1.getTotalViewTransactions)({
                    userId: (0, validate_1.isSuperAdminParam)(user, userId),
                    topicId: Number(topicId) || undefined,
                    walletId: Number(walletId) || undefined,
                    statusId: Number(statusId) || undefined,
                    installmentId: Number(installmentId) || undefined,
                    regularlyId: Number(regularlyId) || undefined,
                    transactionType: Number(transactionType) || undefined,
                });
                res.status(200).json((0, response_1.objectResponse)(response));
            }
        }
    }
    catch (err) {
        console.log(err);
        res.status(500).send((0, response_1.errorResponse)(err, 'Server Error'));
    }
});
