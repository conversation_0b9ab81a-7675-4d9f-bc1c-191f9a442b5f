-- AlterTable
ALTER TABLE "transactions" ADD COLUMN     "swap_id" INTEGER;

-- CreateTable
CREATE TABLE "transaction_swaps" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT DEFAULT '',
    "create_at" TIMESTAMP(3) NOT NULL,
    "update_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "transaction_swaps_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "transactions" ADD CONSTRAINT "transactions_swap_id_fkey" FOREIGN KEY ("swap_id") REFERENCES "transaction_swaps"("id") ON DELETE CASCADE ON UPDATE CASCADE;
