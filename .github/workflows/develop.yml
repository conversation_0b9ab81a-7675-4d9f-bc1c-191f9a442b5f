name: Moneylab Api Develop CI

on:
  push:
    branches:
      - develop

env:
  IMAGE_NAME: narongsakk59/moneylab-api-dev
  DOCKER_FILE_DIR: ./api-dev.Dockerfile

jobs:
  build:
    name: Build Image
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Dashboard to DockerHub
        uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_PASSWORD }}

#      - name: Docker meta
#        id: meta
#        uses: docker/metadata-action@v4
#        with:
#          images: ${{ env.IMAGE_NAME }}
#          tags: |
#            type=schedule
#            type=semver,pattern={{version}}
#            type=semver,pattern={{major}}.{{minor}}
#            type=semver,pattern={{major}}
#            type=sha

      - name: Build and push
        id: docker_build
        uses: docker/build-push-action@v2
        with:
          context: ./
          file: ${{ env.DOCKER_FILE_DIR }}
          builder: ${{ steps.buildx.outputs.name }}
          push: true
          tags: narongsakk59/moneylab-api-dev:latest
#          tags: ${{ steps.meta.outputs.tags }}

      - name: Image digest
        run: echo ${{ steps.docker_build.outputs.digest }}

#  deploy:
#    needs: [ build ]
#    name: Restart Deployment
#    runs-on: ubuntu-latest
#
#    steps:
#      - name: Checkout
#        uses: actions/checkout@v2
#
#      - name: Run new docker image
#        uses: appleboy/ssh-action@v1.0.3
#        with:
#          host: ${{ secrets.SERVER_HOST }}
#          username: ${{ secrets.SERVER_USERNAME }}
#          key: ${{ secrets.SERVER_PRIVATE_KEY }}
#          script: |
#            echo "run command"
#            cd /docker-file
#            docker stop moneylab-api-dev
#            docker container rm moneylab-api-dev
#            docker image rm narongsakk59/moneylab-api-dev
#            docker-compose -f moneylab-api-docker-compose.yml up -d