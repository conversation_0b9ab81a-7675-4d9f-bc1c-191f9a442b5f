import cors from 'cors';
import express from 'express';
import morgan from 'morgan';
import bodyParse from 'body-parser';
import { readdirSync } from 'fs';

const app = express();

app.use(morgan('dev'));
app.use(cors());
app.use(bodyParse.json({ limit: '10mb' }));

readdirSync(process.env.ROUTES_PATH || './src/Routes').map((r) => {
  const split: any[] = r.split('.');
  const route_name = `${split[0]}.${split[1]}`;
  app.use('/api', require(`./Routes/${route_name}`));
});

const PORT = process.env.PORT || 4000;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));
