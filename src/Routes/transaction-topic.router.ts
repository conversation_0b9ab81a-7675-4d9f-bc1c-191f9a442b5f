import { Router } from 'express';

const router = Router();
const {
  List,
  Detail,
  Create,
  Update,
  Remove,
} = require('../Controller/transaction-topic.controller');
const { auth } = require('../Middleware/auth.middleware');

const PATH: string = `/transaction-topic`;

router.get(`${PATH}`, auth, List);
router.get(`${PATH}/:id`, auth, Detail);
router.post(`${PATH}`, auth, Create);
router.put(`${PATH}/:id`, auth, Update);
router.delete(`${PATH}/:id`, auth, Remove);

module.exports = router;
