import { Router } from 'express';

const router = Router();
const {
  List,
  Detail,
  Create,
  Update,
  Remove,
  Import,
} = require('../Controller/transaction.controller');
const { auth } = require('../Middleware/auth.middleware');

const PATH: string = `/transaction`;

router.get(`${PATH}`, auth, List);
router.get(`${PATH}/:id`, auth, Detail);
router.post(`${PATH}`, auth, Create);
router.put(`${PATH}/:id`, auth, Update);
router.delete(`${PATH}/:id`, auth, Remove);
router.post(`${PATH}/import`, auth, Import);

module.exports = router;
