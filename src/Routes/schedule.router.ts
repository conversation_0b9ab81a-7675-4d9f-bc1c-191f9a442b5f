import { Router } from 'express';

const router = Router();
const {
  MonthViewList,
  YearViewList,
  TotalViewList,
  Summary,
} = require('../Controller/schedule.controller');
const { auth } = require('../Middleware/auth.middleware');

const PATH: string = `/schedule`;

router.get(`${PATH}/month`, auth, MonthViewList);
router.get(`${PATH}/year`, auth, YearViewList);
router.get(`${PATH}/summary`, auth, Summary);
router.get(`${PATH}/total`, auth, TotalViewList);

module.exports = router;
