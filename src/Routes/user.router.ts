import { Router } from 'express';

const router = Router();
const {
  List,
  Detail,
  Update,
  Remove,
} = require('../Controller/user.controller');
const { auth } = require('../Middleware/auth.middleware');

const PATH: string = `/user`;

router.get(`${PATH}`, auth, List);
router.get(`${PATH}/:id`, auth, Detail);
router.put(`${PATH}/:id`, auth, Update);
router.delete(`${PATH}/:id`, auth, Remove);

module.exports = router;
