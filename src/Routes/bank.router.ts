import { Router } from 'express';

const router = Router();
const {
  List,
  Create,
  Update,
  Remove,
} = require('../Controller/bank.controller');
const { auth } = require('../Middleware/auth.middleware');

const PATH: string = `/bank`;

router.get(`${PATH}`, auth, List);
router.post(`${PATH}`, auth, Create);
router.put(`${PATH}/:id`, auth, Update);
router.delete(`${PATH}/:id`, auth, Remove);

module.exports = router;
