import { Request, Response } from 'express';
import { errorResponse, objectResponse } from '../Utils/response';
import { getUserByToken } from '../Services/user.service';
import {
  getMonthViewTransactions,
  getSummary,
  getTotalViewTransactions,
  getYearViewTransactions,
} from '../Services/schedule.service';
import { isSuperAdminParam } from '../Utils/validate';

exports.MonthViewList = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization;
    const {
      walletId,
      topicId,
      statusId,
      installmentId,
      regularlyId,
      userId,
      startDate,
      endDate,
      transactionType,
    } = req.query;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const response: any = await getMonthViewTransactions({
          userId: isSuperAdminParam(user, userId),
          topicId: Number(topicId) || undefined,
          walletId: Number(walletId) || undefined,
          statusId: Number(statusId) || undefined,
          installmentId: Number(installmentId) || undefined,
          regularlyId: Number(regularlyId) || undefined,
          transactionType: Number(transactionType) || undefined,
          startDate:
            startDate === undefined ? undefined : new Date(String(startDate)),
          endDate:
            endDate === undefined ? undefined : new Date(String(endDate)),
        });
        res.status(200).json(objectResponse(response));
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.YearViewList = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization;
    const {
      walletId,
      topicId,
      statusId,
      installmentId,
      regularlyId,
      userId,
      year,
      transactionType,
    } = req.query;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const response: any = await getYearViewTransactions({
          userId: isSuperAdminParam(user, userId),
          topicId: Number(topicId) || undefined,
          walletId: Number(walletId) || undefined,
          statusId: Number(statusId) || undefined,
          installmentId: Number(installmentId) || undefined,
          regularlyId: Number(regularlyId) || undefined,
          transactionType: Number(transactionType) || undefined,
          startDate:
            year === undefined ? undefined : new Date(String(`${year}-01-01`)),
          endDate:
            year === undefined ? undefined : new Date(String(`${year}-12-31`)),
        });
        res.status(200).json(objectResponse(response));
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Summary = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization;
    const {
      walletId,
      topicId,
      statusId,
      installmentId,
      regularlyId,
      userId,
      startDate,
      endDate,
      transactionType,
    } = req.query;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const response: any = await getSummary({
          userId: isSuperAdminParam(user, userId),
          topicId: Number(topicId) || undefined,
          walletId: Number(walletId) || undefined,
          statusId: Number(statusId) || undefined,
          installmentId: Number(installmentId) || undefined,
          regularlyId: Number(regularlyId) || undefined,
          transactionType: Number(transactionType) || undefined,
          startDate:
            startDate === undefined ? undefined : new Date(String(startDate)),
          endDate:
            endDate === undefined ? undefined : new Date(String(endDate)),
        });
        res.status(200).json(objectResponse(response));
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.TotalViewList = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization;
    const {
      walletId,
      topicId,
      statusId,
      installmentId,
      regularlyId,
      userId,
      transactionType,
    } = req.query;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const response: any = await getTotalViewTransactions({
          userId: isSuperAdminParam(user, userId),
          topicId: Number(topicId) || undefined,
          walletId: Number(walletId) || undefined,
          statusId: Number(statusId) || undefined,
          installmentId: Number(installmentId) || undefined,
          regularlyId: Number(regularlyId) || undefined,
          transactionType: Number(transactionType) || undefined,
        });
        res.status(200).json(objectResponse(response));
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};
