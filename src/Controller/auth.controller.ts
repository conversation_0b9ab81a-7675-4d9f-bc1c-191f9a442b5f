import { Request, Response } from 'express';
import { checkUser, register } from '../Services/auth.service';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { getUserByToken, getUserDetail } from '../Services/user.service';
import {
  errorResponse,
  errorUndifined,
  objectResponse,
} from '../Utils/response';
import { importGroups } from '../Services/transaction-group.service';

exports.register = async (req: Request, res: Response) => {
  try {
    // 1.CheckUser
    const { username, password } = req.body;
    const user = await checkUser(username);
    if (user) {
      res.status(400).json({ message: 'User Already Exists!!!' });
    } else {
      // 2.Encrypt
      const salt = await bcrypt.genSalt(10);
      const newUser: any = req.body;
      newUser.password = await bcrypt.hash(password, salt);
      // 3.Save
      const createUser = await register(newUser);
      if (createUser) {
        const import_group = await importGroups(createUser.id);
        console.log(import_group);
      }
      res.status(200).json({ message: 'Register Success!!', data: createUser });
    }
  } catch (err) {
    console.log(err);
    res.status(500).send('Server Error');
  }
};

exports.login = async (req: Request, res: Response) => {
  try {
    // 1.CheckUser
    const { username, password } = req.body;
    const user = await checkUser(username);
    if (user) {
      const isMatch = await bcrypt.compare(password, user.password);
      if (!isMatch) {
        res.status(400).json({ message: 'Password Invalid!!!' });
      }
      // 2. Payload
      const payload = {
        user: {
          id: user.id,
          username: user.username,
        },
      };
      // 3. Generate
      jwt.sign(
        payload,
        'moneylabsecret',
        // { expiresIn: 60 * 60 * 24 },
        // { expiresIn: 60 * 60 * 24 * 7 },
        (err, token) => {
          if (err) throw err;
          res.status(200).json({ message: 'Login Success!!', token: token });
        },
      );
    } else {
      res.status(400).json({ message: 'Username Invalid!!!' });
    }
  } catch (err) {
    console.log(err);
    res.status(500).send('Server Error');
  }
};

exports.getMe = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const response = await getUserDetail(user.id);
        if (response) {
          res.status(200).json(objectResponse(response));
        } else {
          res.status(401).json(errorUndifined(null, 'user undefined'));
        }
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};
