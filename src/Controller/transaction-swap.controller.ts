import { Request, Response } from 'express';
import {
  errorResponse,
  objectResponse,
  paginationResponse,
} from '../Utils/response';

import {
  createTransactionSwap,
  deleteTransactionSwap,
  getTransactionSwaps,
} from '../Services/transaction-swap.service';
import { getUserByToken } from '../Services/user.service';
import { isSuperAdminParam } from '../Utils/validate';

exports.List = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization;
    const { page, size, search, userId, startDate, endDate } = req.query;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const response: any = await getTransactionSwaps({
          page: Number(page) || 1,
          size: Number(size) || 20,
          search: search === undefined || search === null ? '' : String(search),
          userId: isSuperAdminParam(user, userId),
          startDate:
            startDate === undefined ? undefined : new Date(String(startDate)),
          endDate:
            endDate === undefined ? undefined : new Date(String(endDate)),
        });
        res
          .status(200)
          .json(
            paginationResponse(
              page || 1,
              size || 20,
              response.amount,
              response.data,
            ),
          );
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Create = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const data = await req.body;
        data.user_id = await user.id;
        const response = await createTransactionSwap(data);
        res.status(200).json(objectResponse(response));
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

// exports.Update = async (req: Request, res: Response) => {
//   try {
//     const { id } = req.params;
//     const response = await updateTransactionType(Number(id), req.body);
//     res.status(200).json(objectResponse(response));
//   } catch (err) {
//     console.log(err);
//     res.status(500).send(errorResponse(err, 'Server Error'));
//   }
// };

exports.Remove = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const response = await deleteTransactionSwap(Number(id));
    res.status(200).json(objectResponse(response));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};
