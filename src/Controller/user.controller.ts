import { Request, Response } from 'express';
import {
  getUsers,
  getUserDetail,
  updateUser,
  deleteUser,
} from '../Services/user.service';
import {
  objectResponse,
  paginationResponse,
  errorResponse,
  errorUndifined,
} from '../Utils/response';

exports.List = async (req: Request, res: Response) => {
  try {
    const { page, size, search } = req.query;
    const response = await getUsers({
      page: Number(page) || 1,
      size: Number(size) || 10,
      search: search === undefined || search === null ? '' : String(search),
    });
    res
      .status(200)
      .json(paginationResponse(page, size, response.amount, response.users));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Detail = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const response = await getUserDetail(Number(id));
    if (response) {
      res.status(200).json(objectResponse(response));
    } else {
      res.status(401).json(errorUndifined(null, 'user undefined'));
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Update = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const response = await updateUser(Number(id), req.body);
    res.status(200).json(objectResponse(response));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Remove = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const response = await deleteUser(Number(id));
    res.status(200).json(objectResponse(response));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};
