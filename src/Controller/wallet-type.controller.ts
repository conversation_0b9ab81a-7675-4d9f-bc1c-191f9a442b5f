import { Request, Response } from 'express';
import { errorResponse, objectResponse } from '../Utils/response';
import {
  createWalletType,
  deleteWalletType,
  getWalletTypes,
  updateWalletType,
} from '../Services/wallet-type.service';

exports.List = async (req: Request, res: Response) => {
  try {
    const response = await getWalletTypes();
    res.status(200).json(objectResponse(response));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Create = async (req: Request, res: Response) => {
  try {
    const response = await createWalletType(req.body);
    res.status(200).json(objectResponse(response));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Update = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const response = await updateWalletType(Number(id), req.body);
    res.status(200).json(objectResponse(response));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Remove = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const response = await deleteWalletType(Number(id));
    res.status(200).json(objectResponse(response));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};
