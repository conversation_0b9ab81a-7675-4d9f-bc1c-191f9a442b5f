import { Request, Response } from 'express';
import { errorResponse, objectResponse } from '../Utils/response';

import {
  createRegularlyUnit,
  deleteRegularlyUnit,
  getRegularlyUnitList,
  updateRegularlyUnit,
} from '../Services/regularly-unit.service';

exports.List = async (req: Request, res: Response) => {
  try {
    const response = await getRegularlyUnitList();
    res.status(200).json(objectResponse(response));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Create = async (req: Request, res: Response) => {
  try {
    const response = await createRegularlyUnit(req.body);
    res.status(200).json(objectResponse(response));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Update = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const response = await updateRegularlyUnit(Number(id), req.body);
    res.status(200).json(objectResponse(response));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Remove = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const response = await deleteRegularlyUnit(Number(id));
    res.status(200).json(objectResponse(response));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};
