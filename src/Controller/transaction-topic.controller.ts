import { Request, Response } from 'express';
import {
  errorResponse,
  objectResponse,
  paginationResponse,
} from '../Utils/response';

import { getUserByToken } from '../Services/user.service';
import {
  createTransactionTopic,
  deleteTransactionTopic,
  getTransactionTopicDetail,
  getTransactionTopicList,
  updateTransactionTopic,
  validateUserAndTransactionTopic,
} from '../Services/transaction-topic.service';
import { isSuperAdminParam } from '../Utils/validate';

exports.List = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization;
    const { page, size, search, groupId, userId } = req.query;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const response = await getTransactionTopicList({
          page: Number(page) || 1,
          size: Number(size) || 20,
          search: search === undefined || search === null ? '' : String(search),
          groupId: Number(groupId) || undefined,
          userId: isSuperAdminParam(user, userId),
        });
        res
          .status(200)
          .json(
            paginationResponse(
              page || 1,
              size || 20,
              response.amount,
              response.data,
            ),
          );
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Detail = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization;
    const { id } = req.params;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const response = await getTransactionTopicDetail({
          id: Number(id),
          userId: user.id,
        });
        res.status(200).json(objectResponse(response));
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Create = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const data = await req.body;
        const response = await createTransactionTopic(data);
        res.status(200).json(objectResponse(response));
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Update = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const token = req.headers.authorization;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const validate = await validateUserAndTransactionTopic(
          Number(user.id),
          Number(id),
        );
        if (validate) {
          const response = await updateTransactionTopic(Number(id), req.body);
          res.status(200).json(objectResponse(response));
        }
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Remove = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const token = req.headers.authorization;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const validate = await validateUserAndTransactionTopic(
          Number(user.id),
          Number(id),
        );
        if (validate) {
          const response = await deleteTransactionTopic(Number(id));
          res.status(200).json(objectResponse(response));
        }
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};
