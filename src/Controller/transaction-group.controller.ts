import { Request, Response } from 'express';
import {
  errorResponse,
  objectResponse,
  paginationResponse,
} from '../Utils/response';

import { getUserByToken } from '../Services/user.service';
import {
  createTransactionGroup,
  deleteTransactionGroup,
  getTransactionGroupDetail,
  getTransactionGroupList,
  updateTransactionGroup,
  validateUserAndTransactionGroup,
} from '../Services/transaction-group.service';
import { isSuperAdminParam } from '../Utils/validate';

exports.List = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization;
    const { page, size, search, typeId, userId } = req.query;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const response = await getTransactionGroupList({
          page: Number(page) || 1,
          size: Number(size) || 20,
          search: search === undefined || search === null ? '' : String(search),
          userId: isSuperAdminParam(user, userId),
          typeId: Number(typeId) || undefined,
        });
        res
          .status(200)
          .json(
            paginationResponse(
              page || 1,
              size || 20,
              response.amount,
              response.data,
            ),
          );
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Detail = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization;
    const { id } = req.params;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const response = await getTransactionGroupDetail({
          id: Number(id),
          userId: user.id,
        });
        res.status(200).json(objectResponse(response));
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Create = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const data = await req.body;
        data.user_id = await user.id;
        const response = await createTransactionGroup(data);
        res.status(200).json(objectResponse(response));
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Update = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const token = req.headers.authorization;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const validate = await validateUserAndTransactionGroup(
          Number(user.id),
          Number(id),
        );
        if (validate) {
          const response = await updateTransactionGroup(Number(id), req.body);
          res.status(200).json(objectResponse(response));
        }
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Remove = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const token = req.headers.authorization;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const validate = await validateUserAndTransactionGroup(
          Number(user.id),
          Number(id),
        );
        if (validate) {
          const response = await deleteTransactionGroup(Number(id));
          res.status(200).json(objectResponse(response));
        }
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};
