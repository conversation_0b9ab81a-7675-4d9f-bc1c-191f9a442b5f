import { Request, Response } from 'express';
import {
  errorResponse,
  objectResponse,
  paginationResponse,
} from '../Utils/response';
import { getUserByToken } from '../Services/user.service';
import {
  createTransaction,
  deleteTransaction,
  getTransactionDetail,
  getTransactionList,
  updateTransaction,
  validateUserAndTransaction,
} from '../Services/transaction.service';
import { generate_array } from '../Utils/import';
import _ from 'lodash';
import { isSuperAdminParam } from '../Utils/validate';

exports.List = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization;
    const {
      page,
      size,
      search,
      walletId,
      topicId,
      statusId,
      installmentId,
      regularlyId,
      userId,
      startDate,
      endDate,
    } = req.query;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const response: any = await getTransactionList({
          page: Number(page) || 1,
          size: Number(size) || 20,
          search: search === undefined || search === null ? '' : String(search),
          userId: isSuperAdminParam(user, userId),
          topicId: Number(topicId) || undefined,
          walletId: Number(walletId) || undefined,
          statusId: Number(statusId) || undefined,
          installmentId: Number(installmentId) || undefined,
          regularlyId: Number(regularlyId) || undefined,
          startDate:
            startDate === undefined ? undefined : new Date(String(startDate)),
          endDate:
            endDate === undefined ? undefined : new Date(String(endDate)),
        });
        res
          .status(200)
          .json(
            paginationResponse(
              page || 1,
              size || 20,
              response.amount,
              response.data,
            ),
          );
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Detail = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization;
    const { id } = req.params;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const response = await getTransactionDetail({
          id: Number(id),
          userId: user.id,
        });
        res.status(200).json(objectResponse(response));
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Create = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const data = await req.body;
        const response = await createTransaction(data);
        res.status(200).json(objectResponse(response));
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Update = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const token = req.headers.authorization;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const validate = await validateUserAndTransaction(
          Number(user.id),
          Number(id),
        );
        if (validate) {
          const response = await updateTransaction(Number(id), req.body);
          res.status(200).json(objectResponse(response));
        }
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Remove = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const token = req.headers.authorization;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const validate = await validateUserAndTransaction(
          Number(user.id),
          Number(id),
        );
        if (validate) {
          const response = await deleteTransaction(Number(id));
          res.status(200).json(objectResponse(response));
        }
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Import = async (req: Request, res: Response) => {
  try {
    const data = await generate_array();
    const array_filter = await _.filter(data.array, (o: any) => {
      return o.amounts > 0;
    });
    // const response = await importTransaction(array_filter);
    res
      .status(200)
      .json({ count: array_filter.length, raw: data.raw, data: array_filter });
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};
