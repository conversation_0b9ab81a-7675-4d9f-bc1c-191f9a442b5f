import { Request, Response } from 'express';
import { errorResponse, objectResponse } from '../Utils/response';
import {
  createTransactionType,
  deleteTransactionType,
  getTransactionTypeList,
  updateTransactionType,
} from '../Services/transaction-type.service';

exports.List = async (req: Request, res: Response) => {
  try {
    const response = await getTransactionTypeList();
    res.status(200).json(objectResponse(response));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Create = async (req: Request, res: Response) => {
  try {
    const response = await createTransactionType(req.body);
    res.status(200).json(objectResponse(response));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Update = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const response = await updateTransactionType(Number(id), req.body);
    res.status(200).json(objectResponse(response));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Remove = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const response = await deleteTransactionType(Number(id));
    res.status(200).json(objectResponse(response));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};
