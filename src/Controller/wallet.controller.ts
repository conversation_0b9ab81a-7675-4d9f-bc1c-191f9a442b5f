import { Request, Response } from 'express';
import {
  errorResponse,
  objectResponse,
  paginationResponse,
} from '../Utils/response';

import {
  createWallet,
  deleteWallet,
  getWalletDetail,
  getWallets,
  updateWallet,
  validateUserAndWallet,
} from '../Services/wallet.service';
import { getUserByToken } from '../Services/user.service';

exports.List = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization;
    const { page, size, search, bankId, typeId } = req.query;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const response = await getWallets({
          page: Number(page) || 1,
          size: Number(size) || 20,
          search: search === undefined || search === null ? '' : String(search),
          userId: user.id,
          bankId: Number(bankId) || undefined,
          typeId: Number(typeId) || undefined,
        });
        res
          .status(200)
          .json(
            paginationResponse(
              page || 1,
              size || 20,
              response.amount,
              response.data,
            ),
          );
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Detail = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization;
    const { id } = req.params;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const response = await getWalletDetail({
          id: Number(id),
          userId: user.id,
        });
        res.status(200).json(objectResponse(response));
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Create = async (req: Request, res: Response) => {
  try {
    const token = req.headers.authorization;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const data = await req.body;
        data.user_id = await user.id;
        const response = await createWallet(data);
        res.status(200).json(objectResponse(response));
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Update = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const token = req.headers.authorization;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const validate = await validateUserAndWallet(
          Number(user.id),
          Number(id),
        );
        if (validate) {
          const response = await updateWallet(Number(id), req.body);
          res.status(200).json(objectResponse(response));
        }
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Remove = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const token = req.headers.authorization;
    if (token) {
      const user: any = await getUserByToken(String(token));
      if (user) {
        const validate = await validateUserAndWallet(
          Number(user.id),
          Number(id),
        );
        if (validate) {
          const response = await deleteWallet(Number(id));
          res.status(200).json(objectResponse(response));
        }
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};
