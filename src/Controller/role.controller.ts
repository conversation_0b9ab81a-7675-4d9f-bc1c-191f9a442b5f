import { Request, Response } from 'express';
import {
  createRole,
  deleteRole,
  getRoles,
  updateRole,
} from '../Services/role.service';
import { errorResponse, objectResponse } from '../Utils/response';

exports.List = async (req: Request, res: Response) => {
  try {
    const response = await getRoles();
    res.status(200).json(objectResponse(response));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Create = async (req: Request, res: Response) => {
  try {
    const response = await createRole(req.body);
    res.status(200).json(objectResponse(response));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Update = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const response = await updateRole(Number(id), req.body);
    res.status(200).json(objectResponse(response));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};

exports.Remove = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const response = await deleteRole(Number(id));
    res.status(200).json(objectResponse(response));
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Server Error'));
  }
};
