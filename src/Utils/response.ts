export const errorResponse = (err: any, message: string) => {
  return {
    status: false,
    code: 500,
    message: message,
    err: err,
  };
};

export const errorUndifined = (err: any, message: string) => {
  return {
    status: false,
    code: 401,
    message: message,
    err: err,
  };
};

export const paginationResponse = (
  page: any,
  size: any,
  total: any,
  data: any,
) => {
  return {
    status: true,
    message: 'Success',
    code: 200,
    isPagination: true,
    data: {
      current_page: Number(page) || 1,
      pages: Math.ceil(Number(total) / Number(size)),
      size: Number(size) || 10,
      total: Number(total) || 0,
      content: data || [],
    },
  };
};

export const objectResponse = (data: any) => {
  return {
    status: true,
    message: 'Success',
    code: 200,
    isPagination: false,
    data: data || {},
  };
};
