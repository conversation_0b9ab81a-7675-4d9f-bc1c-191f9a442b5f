import XLSX from 'xlsx';

export const generate_array = async () => {
  const webhook = await XLSX.readFile('./src/tester.xlsx');
  const sheetName = await webhook.SheetNames[6];
  const data: any[] = await XLSX.utils.sheet_to_json(
    webhook.Sheets[sheetName],
    {
      raw: false,
    },
  );
  const start = 7;
  const end = 371;
  const arr = [];

  for (let i = start; i <= end; i++) {
    const array = [
      {
        name: 'เงินเดือน',
        current_balance: Number(data[i].__EMPTY_19) || 0,
        amounts: Number(data[i].__EMPTY_4) || 0,
        transaction_date_time: data[i].__EMPTY_3,
        wallet_id: 1,
        topic_id: 54,
        status_id: 2,
        create_at: data[i].__EMPTY_3,
        update_at: data[i].__EMPTY_3,
      },
      {
        name: 'โบนัส',
        current_balance: Number(data[i].__EMPTY_19) || 0,
        amounts: Number(data[i].__EMPTY_5) || 0,
        transaction_date_time: data[i].__EMPTY_3,
        wallet_id: 1,
        topic_id: 55,
        status_id: 2,
        create_at: data[i].__EMPTY_3,
        update_at: data[i].__EMPTY_3,
      },
      {
        name: 'อื่น ๆ',
        current_balance: Number(data[i].__EMPTY_19) || 0,
        amounts: Number(data[i].__EMPTY_6) || 0,
        transaction_date_time: data[i].__EMPTY_3,
        wallet_id: 1,
        topic_id: 71,
        status_id: 2,
        create_at: data[i].__EMPTY_3,
        update_at: data[i].__EMPTY_3,
      },
      {
        name: 'ค่าเช่า / ผ่อน / ค่าไฟ-น้ำ',
        current_balance: Number(data[i].__EMPTY_19) || 0,
        amounts: Number(data[i].__EMPTY_7) || 0,
        transaction_date_time: data[i].__EMPTY_3,
        wallet_id: 1,
        topic_id: 42,
        status_id: 2,
        create_at: data[i].__EMPTY_3,
        update_at: data[i].__EMPTY_3,
      },
      {
        name: 'อาหาร',
        current_balance: Number(data[i].__EMPTY_19) || 0,
        amounts: Number(data[i].__EMPTY_8) || 0,
        transaction_date_time: data[i].__EMPTY_3,
        wallet_id: 1,
        topic_id: 38,
        status_id: 2,
        create_at: data[i].__EMPTY_3,
        update_at: data[i].__EMPTY_3,
      },
      {
        name: 'ของใช้',
        current_balance: Number(data[i].__EMPTY_19) || 0,
        amounts: Number(data[i].__EMPTY_9) || 0,
        transaction_date_time: data[i].__EMPTY_3,
        wallet_id: 1,
        topic_id: 58,
        status_id: 2,
        create_at: data[i].__EMPTY_3,
        update_at: data[i].__EMPTY_3,
      },
      {
        name: 'Steaming',
        current_balance: Number(data[i].__EMPTY_19) || 0,
        amounts: Number(data[i].__EMPTY_10) || 0,
        transaction_date_time: data[i].__EMPTY_3,
        wallet_id: 1,
        topic_id: 44,
        status_id: 2,
        create_at: data[i].__EMPTY_3,
        update_at: data[i].__EMPTY_3,
      },
      {
        name: 'โทรศัพท์ / อิ้นเทอร์เน็ต',
        current_balance: Number(data[i].__EMPTY_19) || 0,
        amounts: Number(data[i].__EMPTY_11) || 0,
        transaction_date_time: data[i].__EMPTY_3,
        wallet_id: 1,
        topic_id: 74,
        status_id: 2,
        create_at: data[i].__EMPTY_3,
        update_at: data[i].__EMPTY_3,
      },
      {
        name: 'ของใช้ ผ่อนชำระ',
        current_balance: Number(data[i].__EMPTY_19) || 0,
        amounts: Number(data[i].__EMPTY_12) || 0,
        transaction_date_time: data[i].__EMPTY_3,
        wallet_id: 1,
        topic_id: 62,
        status_id: 2,
        create_at: data[i].__EMPTY_3,
        update_at: data[i].__EMPTY_3,
      },
      {
        name: 'Cloud Server',
        current_balance: Number(data[i].__EMPTY_19) || 0,
        amounts: Number(data[i].__EMPTY_13) || 0,
        transaction_date_time: data[i].__EMPTY_3,
        wallet_id: 1,
        topic_id: 40,
        status_id: 2,
        create_at: data[i].__EMPTY_3,
        update_at: data[i].__EMPTY_3,
      },
      {
        name: 'รักษาพยาบาล / ยา',
        current_balance: Number(data[i].__EMPTY_19) || 0,
        amounts: Number(data[i].__EMPTY_14) || 0,
        transaction_date_time: data[i].__EMPTY_3,
        wallet_id: 1,
        topic_id: 37,
        status_id: 2,
        create_at: data[i].__EMPTY_3,
        update_at: data[i].__EMPTY_3,
      },
      {
        name: 'การเดินทาง',
        current_balance: Number(data[i].__EMPTY_19) || 0,
        amounts: Number(data[i].__EMPTY_15) || 0,
        transaction_date_time: data[i].__EMPTY_3,
        wallet_id: 1,
        topic_id: 64,
        status_id: 2,
        create_at: data[i].__EMPTY_3,
        update_at: data[i].__EMPTY_3,
      },
      {
        name: 'เสื้อผ้า',
        current_balance: Number(data[i].__EMPTY_19) || 0,
        amounts: Number(data[i].__EMPTY_16) || 0,
        transaction_date_time: data[i].__EMPTY_3,
        wallet_id: 1,
        topic_id: 63,
        status_id: 2,
        create_at: data[i].__EMPTY_3,
        update_at: data[i].__EMPTY_3,
      },
      {
        name: 'อื่น ๆ',
        current_balance: Number(data[i].__EMPTY_19) || 0,
        amounts: Number(data[i].__EMPTY_17) || 0,
        transaction_date_time: data[i].__EMPTY_3,
        wallet_id: 1,
        topic_id: 77,
        status_id: 2,
        create_at: data[i].__EMPTY_3,
        update_at: data[i].__EMPTY_3,
      },
    ];
    arr.push(...array);
  }
  return { raw: data[7], array: arr };
};
