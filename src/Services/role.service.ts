import { PrismaClient, roles } from '@prisma/client';
import { current_datetime } from '../Utils/time';
const prisma = new PrismaClient();
export const getRoles = async () => {
  const response: roles[] = await prisma.roles.findMany({
    orderBy: {
      id: 'asc',
    },
    include: {
      users: {
        select: {
          id: true,
          username: true,
          name: true,
        },
      },
    },
  });
  return response;
};

export const createRole = async (data: roles | any) => {
  data.create_at = current_datetime();
  data.update_at = current_datetime();
  const response: roles = await prisma.roles.create({
    data: data,
  });
  return response;
};

export const updateRole = async (id: number, data: any) => {
  data.update_at = current_datetime();
  const response: roles = await prisma.roles.update({
    where: {
      id: id,
    },
    data: data,
  });
  return response;
};

export const deleteRole = async (id: number) => {
  const response: roles = await prisma.roles.delete({
    where: {
      id: id,
    },
  });
  return response;
};
