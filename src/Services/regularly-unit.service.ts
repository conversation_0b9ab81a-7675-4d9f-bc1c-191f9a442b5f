import { PrismaClient, regularly_unit } from '@prisma/client';
import { current_datetime } from '../Utils/time';
const prisma = new PrismaClient();
export const getRegularlyUnitList = async () => {
  const response: regularly_unit[] = await prisma.regularly_unit.findMany({
    include: {
      regularly: true,
    },
  });
  return response;
};

export const createRegularlyUnit = async (data: regularly_unit | any) => {
  data.create_at = current_datetime();
  data.update_at = current_datetime();
  const response: regularly_unit = await prisma.regularly_unit.create({
    data: data,
  });
  return response;
};

export const updateRegularlyUnit = async (id: number, data: any) => {
  data.update_at = current_datetime();
  const response: regularly_unit = await prisma.regularly_unit.update({
    where: {
      id: id,
    },
    data: data,
  });
  return response;
};

export const deleteRegularlyUnit = async (id: number) => {
  const response: regularly_unit = await prisma.regularly_unit.delete({
    where: {
      id: id,
    },
  });
  return response;
};
