import { PrismaClient, users } from '@prisma/client';
import { current_datetime } from '../Utils/time';
const prisma = new PrismaClient();

export const checkUser = async (username: string) => {
  const response: users | null = await prisma.users.findFirst({
    where: { username: username },
  });
  return response;
};

export const register = async (data: users | any) => {
  data.create_at = current_datetime();
  data.update_at = current_datetime();
  const response: users = await prisma.users.create({
    data: data,
  });
  return response;
};
