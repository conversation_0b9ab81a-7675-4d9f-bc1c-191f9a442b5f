import { PrismaClient, users } from '@prisma/client';
import jwt from 'jsonwebtoken';
import { current_datetime } from '../Utils/time';
const prisma = new PrismaClient();
export const getUsers = async (params: {
  page: number;
  size: number;
  search: string;
}) => {
  const { page, size, search } = params;
  const response: any = await prisma.users.findMany({
    skip: (page - 1) * size,
    take: size,
    orderBy: {
      id: 'asc',
    },
    where: {
      username: {
        contains: search,
      },
    },
    select: {
      id: true,
      username: true,
      password: false,
      name: true,
      first_name: false,
      lastname_name: false,
      email: true,
      img_url: true,
      create_at: true,
      update_at: true,
      role: {
        select: {
          id: true,
          name: true,
          isSuperAdmin: true,
          isAdmin: true,
          isUser: true,
          isVisitor: true,
        },
      },
    },
    // include: {
    //   role: {
    //     select: {
    //       id: true,
    //       name: true,
    //       isSuperadmin: true,
    //       isAdmin: true,
    //       isUser: true,
    //       isVisitor: true,
    //       // users: {
    //       //   select: {
    //       //     id: true,
    //       //     name: true,
    //       //     username: true,
    //       //     img_url: true,
    //       //   },
    //       // },
    //     },
    //   },
    // },
  });
  const amount = await prisma.users.count({
    where: {
      username: {
        contains: search,
      },
    },
  });
  return { users: response, amount: amount };
};

export const getUserDetail = async (id: number) => {
  const response = await prisma.users.findFirst({
    where: {
      id: id,
    },
    select: {
      id: true,
      username: true,
      password: false,
      name: true,
      first_name: false,
      lastname_name: false,
      email: true,
      img_url: true,
      create_at: true,
      update_at: true,
      role: {
        select: {
          id: true,
          name: true,
          isSuperAdmin: true,
          isAdmin: true,
          isUser: true,
          isVisitor: true,
        },
      },
    },
  });
  return response;
};

export const updateUser = async (id: number, data: any) => {
  data.update_at = current_datetime();
  const response: users = await prisma.users.update({
    where: {
      id: id,
    },
    data: data,
  });
  return response;
};

export const deleteUser = async (id: number) => {
  const response: users = await prisma.users.delete({
    where: {
      id: id,
    },
  });
  return response;
};

export const getUserByToken = async (token: string) => {
  const tk = String(token).split(' ');
  const decoded: any = jwt.verify(tk[1], 'moneylabsecret');
  if (decoded) {
    const response = await prisma.users.findFirst({
      where: {
        id: decoded.user.id,
      },
      select: {
        id: true,
        username: true,
        password: false,
        name: true,
        first_name: false,
        lastname_name: false,
        email: true,
        img_url: true,
        create_at: true,
        update_at: true,
        role: {
          select: {
            id: true,
            name: true,
            isSuperAdmin: true,
            isAdmin: true,
            isUser: true,
            isVisitor: true,
          },
        },
      },
    });
    return response;
  } else {
    return null;
  }
};
