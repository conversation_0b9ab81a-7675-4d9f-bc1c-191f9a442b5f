import { PrismaClient, regularly_type } from '@prisma/client';
import { current_datetime } from '../Utils/time';
const prisma = new PrismaClient();
export const getRegularlyTypeList = async () => {
  const response: regularly_type[] = await prisma.regularly_type.findMany({
    include: {
      regularly: true,
    },
  });
  return response;
};

export const createRegularlyType = async (data: regularly_type | any) => {
  data.create_at = current_datetime();
  data.update_at = current_datetime();
  const response: regularly_type = await prisma.regularly_type.create({
    data: data,
  });
  return response;
};

export const updateRegularlyType = async (id: number, data: any) => {
  data.update_at = current_datetime();
  const response: regularly_type = await prisma.regularly_type.update({
    where: {
      id: id,
    },
    data: data,
  });
  return response;
};

export const deleteRegularlyType = async (id: number) => {
  const response: regularly_type = await prisma.regularly_type.delete({
    where: {
      id: id,
    },
  });
  return response;
};
