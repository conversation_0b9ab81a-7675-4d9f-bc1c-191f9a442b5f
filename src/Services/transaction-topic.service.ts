import { PrismaClient, transaction_topics } from '@prisma/client';
import { current_datetime } from '../Utils/time';
const prisma = new PrismaClient();
export const getTransactionTopicList = async (params: {
  page: number;
  size: number;
  search?: string;
  groupId?: number;
  userId?: number;
}) => {
  const { page, size, search, groupId, userId } = params;
  const response: transaction_topics[] =
    await prisma.transaction_topics.findMany({
      skip: (page - 1) * size,
      take: size,
      orderBy: {
        id: 'asc',
      },
      where: {
        name: {
          contains: search,
        },
        transaction_group_id: groupId,
        transaction_group: {
          user_id: userId,
        },
      },
      include: {
        transactions: true,
        transaction_group: true,
      },
    });
  const amount = await prisma.transaction_topics.count({
    where: {
      name: {
        contains: search,
      },
      transaction_group_id: groupId,
    },
  });
  return { data: response, amount: amount };
};

export const getTransactionTopicDetail = async (params: {
  id: number;
  userId: number;
}) => {
  const { userId, id } = params;
  const response: transaction_topics | null =
    await prisma.transaction_topics.findFirst({
      where: {
        transaction_group: {
          user_id: userId,
        },
        id: id,
      },
      include: {
        transactions: true,
      },
    });
  return response;
};

export const createTransactionTopic = async (
  data: transaction_topics | any,
) => {
  data.create_at = current_datetime();
  data.update_at = current_datetime();
  const response: transaction_topics = await prisma.transaction_topics.create({
    data: data,
  });
  return response;
};

export const updateTransactionTopic = async (id: number, data: any) => {
  data.update_at = current_datetime();
  const response: transaction_topics = await prisma.transaction_topics.update({
    where: {
      id: id,
    },
    data: data,
  });
  return response;
};

export const deleteTransactionTopic = async (id: number) => {
  const response: transaction_topics = await prisma.transaction_topics.delete({
    where: {
      id: id,
    },
  });
  return response;
};

export const validateUserAndTransactionTopic = async (
  userId: number,
  transactionTopicId: number,
) => {
  const response: transaction_topics | null =
    await prisma.transaction_topics.findFirst({
      where: {
        id: transactionTopicId,
        transaction_group: {
          user_id: userId,
        },
      },
    });
  return Boolean(response);
};
