import { PrismaClient, transaction_types } from '@prisma/client';
import { current_datetime } from '../Utils/time';
const prisma = new PrismaClient();
export const getTransactionTypeList = async () => {
  const response: transaction_types[] = await prisma.transaction_types.findMany(
    {
      orderBy: {
        id: 'asc',
      },
      include: {
        transaction_groups: true,
      },
    },
  );
  return response;
};

export const createTransactionType = async (data: transaction_types | any) => {
  data.create_at = current_datetime();
  data.update_at = current_datetime();
  const response: transaction_types = await prisma.transaction_types.create({
    data: data,
  });
  return response;
};

export const updateTransactionType = async (id: number, data: any) => {
  data.update_at = current_datetime();
  const response: transaction_types = await prisma.transaction_types.update({
    where: {
      id: id,
    },
    data: data,
  });
  return response;
};

export const deleteTransactionType = async (id: number) => {
  const response: transaction_types = await prisma.transaction_types.delete({
    where: {
      id: id,
    },
  });
  return response;
};
