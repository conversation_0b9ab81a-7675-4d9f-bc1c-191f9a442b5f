import { installments, PrismaClient } from '@prisma/client';
import { current_datetime } from '../Utils/time';
const prisma = new PrismaClient();
export const getInstallmentList = async (params: {
  page: number;
  size: number;
  search?: string;
  topicId?: number;
}) => {
  const { page, size, search, topicId } = params;
  const response: installments[] = await prisma.installments.findMany({
    skip: (page - 1) * size,
    take: size,
    orderBy: {
      id: 'asc',
    },
    where: {
      name: {
        contains: search,
      },
      topic_id: topicId,
    },
    include: {
      transactions: true,
      transaction_topic: true,
    },
  });
  const amount = await prisma.installments.count({
    where: {
      name: {
        contains: search,
      },
      topic_id: topicId,
    },
  });
  return { data: response, amount: amount };
};

export const getInstallmentDetail = async (params: {
  id: number;
  userId: number;
}) => {
  const { userId, id } = params;
  const response: installments | null = await prisma.installments.findFirst({
    where: {
      transaction_topic: {
        transaction_group: {
          user_id: userId,
        },
      },
      id: id,
    },
    include: {
      transactions: true,
    },
  });
  return response;
};

export const createInstallment = async (data: installments | any) => {
  data.create_at = current_datetime();
  data.update_at = current_datetime();
  const response: installments = await prisma.installments.create({
    data: data,
  });
  return response;
};

export const updateInstallment = async (id: number, data: any) => {
  data.update_at = current_datetime();
  const response: installments = await prisma.installments.update({
    where: {
      id: id,
    },
    data: data,
  });
  return response;
};

export const deleteInstallment = async (id: number) => {
  const response: installments = await prisma.installments.delete({
    where: {
      id: id,
    },
  });
  return response;
};

export const validateUserAndInstallment = async (
  userId: number,
  installmentId: number,
) => {
  const response: installments | null = await prisma.installments.findFirst({
    where: {
      id: installmentId,
      transaction_topic: {
        transaction_group: {
          user_id: userId,
        },
      },
    },
  });
  return Boolean(response);
};
