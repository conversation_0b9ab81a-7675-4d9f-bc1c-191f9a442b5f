import { PrismaClient, transactions } from '@prisma/client';
import { current_datetime } from '../Utils/time';
const prisma = new PrismaClient();
export const getTransactionList = async (params: {
  page: number;
  size: number;
  search?: string;
  userId?: number;
  walletId?: number;
  topicId?: number;
  statusId?: number;
  installmentId?: number;
  regularlyId?: number;
  startDate?: Date;
  endDate?: Date;
}) => {
  const {
    page,
    size,
    search,
    userId,
    walletId,
    topicId,
    statusId,
    installmentId,
    regularlyId,
    startDate,
    endDate,
  } = params;

  const response: transactions[] = await prisma.transactions.findMany({
    skip: (page - 1) * size,
    take: size,
    orderBy: {
      transaction_date_time: 'asc',
    },
    where: {
      name: {
        contains: search,
      },
      wallet: {
        user_id: userId,
      },
      wallet_id: walletId,
      topic_id: topicId,
      status_id: statusId,
      installment_id: installmentId,
      regularly_id: regularlyId,
      transaction_date_time: {
        lte: endDate,
        gte: startDate,
      },
    },
    include: {
      wallet: true,
      transaction_topic: {
        include: {
          transaction_group: {
            include: {
              transaction_type: true,
            },
          },
        },
      },
      transaction_status: true,
      installment: true,
      regularly: true,
      transaction_swap: true,
    },
  });
  const amount = await prisma.transactions.count({
    where: {
      name: {
        contains: search,
      },
      wallet: {
        user_id: userId,
      },
      wallet_id: walletId,
      topic_id: topicId,
      status_id: statusId,
      installment_id: installmentId,
      regularly_id: regularlyId,
    },
  });
  return { data: response, amount: amount };
};

export const getTransactionDetail = async (params: {
  id: number;
  userId: number;
}) => {
  const { userId, id } = params;
  const response: transactions | null = await prisma.transactions.findFirst({
    where: {
      id: id,
      wallet: {
        user_id: userId,
      },
    },
    include: {
      wallet: true,
      transaction_topic: true,
      transaction_status: true,
      installment: true,
      regularly: true,
    },
  });
  return response;
};

export const createTransaction = async (data: transactions | any) => {
  data.create_at = current_datetime();
  data.update_at = current_datetime();
  const response: transactions = await prisma.transactions.create({
    data: data,
  });
  return response;
};

export const updateTransaction = async (id: number, data: any) => {
  data.update_at = current_datetime();
  const response: transactions = await prisma.transactions.update({
    where: {
      id: id,
    },
    data: data,
  });
  return response;
};

export const deleteTransaction = async (id: number) => {
  const response: transactions = await prisma.transactions.delete({
    where: {
      id: id,
    },
  });
  return response;
};

export const validateUserAndTransaction = async (
  userId: number,
  transactionId: number,
) => {
  const response: transactions | null = await prisma.transactions.findFirst({
    where: {
      id: transactionId,
      wallet: {
        user_id: userId,
      },
    },
  });
  return Boolean(response);
};

export const importTransaction = async (data: transactions[] | any[]) => {
  // res.create_at = current_datetime();
  // res.update_at = current_datetime();
  console.log(data);
  const response: any = await prisma.transactions.createMany({
    data: data,
  });
  return response;
};
