import { PrismaClient, transaction_status } from '@prisma/client';
import { current_datetime } from '../Utils/time';
const prisma = new PrismaClient();
export const getTransactionStatusList = async () => {
  const response: transaction_status[] =
    await prisma.transaction_status.findMany({
      orderBy: {
        id: 'asc',
      },
    });
  return response;
};

export const createTransactionStatus = async (
  data: transaction_status | any,
) => {
  data.create_at = current_datetime();
  data.update_at = current_datetime();
  const response: transaction_status = await prisma.transaction_status.create({
    data: data,
  });
  return response;
};

export const updateTransactionStatus = async (id: number, data: any) => {
  data.update_at = current_datetime();
  const response: transaction_status = await prisma.transaction_status.update({
    where: {
      id: id,
    },
    data: data,
  });
  return response;
};

export const deleteTransactionStatus = async (id: number) => {
  const response: transaction_status = await prisma.transaction_status.delete({
    where: {
      id: id,
    },
  });
  return response;
};
