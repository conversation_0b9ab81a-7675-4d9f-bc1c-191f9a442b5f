import { PrismaClient, wallet_types } from '@prisma/client';
import { current_datetime } from '../Utils/time';
const prisma = new PrismaClient();
export const getWalletTypes = async () => {
  const response: wallet_types[] = await prisma.wallet_types.findMany({
    include: {
      wallets: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });
  return response;
};

export const createWalletType = async (data: wallet_types | any) => {
  data.create_at = current_datetime();
  data.update_at = current_datetime();
  const response: wallet_types = await prisma.wallet_types.create({
    data: data,
  });
  return response;
};

export const updateWalletType = async (id: number, data: any) => {
  data.update_at = current_datetime();
  const response: wallet_types = await prisma.wallet_types.update({
    where: {
      id: id,
    },
    data: data,
  });
  return response;
};

export const deleteWalletType = async (id: number) => {
  const response: wallet_types = await prisma.wallet_types.delete({
    where: {
      id: id,
    },
  });
  return response;
};
