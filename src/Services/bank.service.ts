import { banks, PrismaClient } from '@prisma/client';
import { current_datetime } from '../Utils/time';
const prisma = new PrismaClient();
export const getBanks = async () => {
  const response: banks[] = await prisma.banks.findMany({
    where: {
      NOT: {
        id: 0,
      },
    },
    orderBy: {
      id: 'asc',
    },
    // include: {
    //   wallets: {
    //     select: {
    //       id: true,
    //       name: true,
    //     },
    //   },
    // },
  });
  return response;
};

export const createBank = async (data: banks | any) => {
  data.create_at = current_datetime();
  data.update_at = current_datetime();
  const response: banks = await prisma.banks.create({
    data: data,
  });
  return response;
};

export const updateBank = async (id: number, data: any) => {
  data.update_at = current_datetime();
  const response: banks = await prisma.banks.update({
    where: {
      id: id,
    },
    data: data,
  });
  return response;
};

export const deleteBank = async (id: number) => {
  const response: banks = await prisma.banks.delete({
    where: {
      id: id,
    },
  });
  return response;
};
