import { PrismaClient, wallets } from '@prisma/client';
import { current_datetime } from '../Utils/time';
const prisma = new PrismaClient();
export const getWallets = async (params: {
  page: number;
  size: number;
  search?: string;
  userId?: number;
  bankId?: number;
  typeId?: number;
}) => {
  const { page, size, search, userId, bankId, typeId } = params;
  const response: wallets[] = await prisma.wallets.findMany({
    skip: (page - 1) * size,
    take: size,
    orderBy: {
      id: 'asc',
    },
    where: {
      name: {
        contains: search,
      },
      user_id: userId,
      bank_id: bankId,
      wallet_type_id: typeId,
    },
    include: {
      transactions: false,
      bank: true,
      wallet_type: true,
    },
  });
  const amount = await prisma.wallets.count({
    where: {
      name: {
        contains: search,
      },
      user_id: userId,
      bank_id: bankId,
      wallet_type_id: typeId,
    },
  });
  return { data: response, amount: amount };
};

export const getWalletDetail = async (params: {
  id: number;
  userId: number;
}) => {
  const { userId, id } = params;
  const response: wallets | null = await prisma.wallets.findFirst({
    where: {
      user_id: userId,
      id: id,
    },
    include: {
      transactions: true,
    },
  });
  return response;
};

export const createWallet = async (data: wallets | any) => {
  data.create_at = current_datetime();
  data.update_at = current_datetime();
  const response: wallets = await prisma.wallets.create({
    data: data,
  });
  return response;
};

export const updateWallet = async (id: number, data: any) => {
  data.update_at = current_datetime();
  const response: wallets = await prisma.wallets.update({
    where: {
      id: id,
    },
    data: data,
  });
  return response;
};

export const deleteWallet = async (id: number) => {
  const response: wallets = await prisma.wallets.delete({
    where: {
      id: id,
    },
  });
  return response;
};

export const validateUserAndWallet = async (
  userId: number,
  walletId: number,
) => {
  const response: wallets | null = await prisma.wallets.findFirst({
    where: {
      id: walletId,
      user_id: userId,
    },
  });
  return Boolean(response);
};
