import { PrismaClient, regularly } from '@prisma/client';
import { current_datetime } from '../Utils/time';
const prisma = new PrismaClient();
export const getRegularlyList = async (params: {
  page: number;
  size: number;
  search?: string;
  userId?: number;
  topicId?: number;
  typeId?: number;
  unitId?: number;
}) => {
  const { page, size, search, userId, topicId, unitId, typeId } = params;
  const response: regularly[] = await prisma.regularly.findMany({
    skip: (page - 1) * size,
    take: size,
    orderBy: {
      id: 'asc',
    },
    where: {
      name: {
        contains: search,
      },
      transaction_topic: {
        transaction_group: {
          user_id: userId,
        },
      },
      topic_id: topicId,
      unit_id: unitId,
      type_id: typeId,
    },
    include: {
      transactions: true,
      type: true,
      unit: true,
      transaction_topic: true,
    },
  });
  const amount = await prisma.regularly.count({
    where: {
      name: {
        contains: search,
      },
      transaction_topic: {
        transaction_group: {
          user_id: userId,
        },
      },
      topic_id: topicId,
      unit_id: unitId,
      type_id: typeId,
    },
  });
  return { data: response, amount: amount };
};

export const getRegularlyDetail = async (params: {
  id: number;
  userId: number;
}) => {
  const { userId, id } = params;
  const response: regularly | null = await prisma.regularly.findFirst({
    where: {
      id: id,
      transaction_topic: {
        transaction_group: {
          user_id: userId,
        },
      },
    },
    include: {
      transactions: true,
      type: true,
      unit: true,
      transaction_topic: true,
    },
  });
  return response;
};

export const createRegularly = async (data: regularly | any) => {
  data.create_at = current_datetime();
  data.update_at = current_datetime();
  const response: regularly = await prisma.regularly.create({
    data: data,
  });
  return response;
};

export const updateRegularly = async (id: number, data: any) => {
  data.update_at = current_datetime();
  const response: regularly = await prisma.regularly.update({
    where: {
      id: id,
    },
    data: data,
  });
  return response;
};

export const deleteRegularly = async (id: number) => {
  const response: regularly = await prisma.regularly.delete({
    where: {
      id: id,
    },
  });
  return response;
};

export const validateUserAndRegularly = async (
  userId: number,
  regularlyId: number,
) => {
  const response: regularly | null = await prisma.regularly.findFirst({
    where: {
      id: regularlyId,
      transaction_topic: {
        transaction_group: {
          user_id: userId,
        },
      },
    },
  });
  return Boolean(response);
};
