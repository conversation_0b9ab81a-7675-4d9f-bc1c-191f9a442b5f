import { PrismaClient, transaction_groups } from '@prisma/client';
import { current_datetime } from '../Utils/time';
const prisma = new PrismaClient();
export const getTransactionGroupList = async (params: {
  page: number;
  size: number;
  search?: string;
  userId?: number;
  typeId?: number;
}) => {
  const { page, size, search, userId, typeId } = params;
  const response: transaction_groups[] =
    await prisma.transaction_groups.findMany({
      skip: (page - 1) * size,
      take: size,
      orderBy: {
        id: 'asc',
      },
      where: {
        name: {
          contains: search,
        },
        user_id: userId,
        transaction_type_id: typeId,
      },
      include: {
        transaction_topics: true,
        transaction_type: true,
      },
    });
  const amount = await prisma.transaction_groups.count({
    where: {
      name: {
        contains: search,
      },
      user_id: userId,
      transaction_type_id: typeId,
    },
  });
  return { data: response, amount: amount };
};

export const getTransactionGroupDetail = async (params: {
  id: number;
  userId: number;
}) => {
  const { userId, id } = params;
  const response: transaction_groups | null =
    await prisma.transaction_groups.findFirst({
      where: {
        user_id: userId,
        id: id,
      },
      include: {
        transaction_topics: true,
      },
    });
  return response;
};

export const createTransactionGroup = async (
  data: transaction_groups | any,
) => {
  data.create_at = current_datetime();
  data.update_at = current_datetime();
  const response: transaction_groups = await prisma.transaction_groups.create({
    data: data,
  });
  return response;
};

export const updateTransactionGroup = async (id: number, data: any) => {
  data.update_at = current_datetime();
  const response: transaction_groups = await prisma.transaction_groups.update({
    where: {
      id: id,
    },
    data: data,
  });
  return response;
};

export const deleteTransactionGroup = async (id: number) => {
  const response: transaction_groups = await prisma.transaction_groups.delete({
    where: {
      id: id,
    },
  });
  return response;
};

export const validateUserAndTransactionGroup = async (
  userId: number,
  transactionGroupId: number,
) => {
  const response: transaction_groups | null =
    await prisma.transaction_groups.findFirst({
      where: {
        id: transactionGroupId,
        user_id: userId,
      },
    });
  return Boolean(response);
};

export const importGroups = async (userId: number) => {
  const response: any[] = await prisma.transaction_groups.findMany({
    orderBy: {
      id: 'asc',
    },
    where: {
      user_id: 2,
    },
    select: {
      name: true,
      transaction_type_id: true,
      transaction_topics: true,
    },
  });
  if (response) {
    response.map(async (g: any) => {
      g.user_id = userId;
      g.create_at = current_datetime();
      g.update_at = current_datetime();
      const group_payload: any = {
        user_id: userId,
        name: g.name,
        transaction_type_id: g.transaction_type_id,
        create_at: current_datetime(),
        update_at: current_datetime(),
      };
      const res: any = await prisma.transaction_groups.create({
        data: group_payload,
      });
      if (g.transaction_topics) {
        await g.transaction_topics.map(async (t: any) => {
          const topic_payload: any = {
            name: t.name,
            transaction_group_id: res.id,
            create_at: current_datetime(),
            update_at: current_datetime(),
          };
          await prisma.transaction_topics.create({
            data: topic_payload,
          });
        });
      }
    });
  }
  return response;
};
