import { Request, Response } from 'express';
import { errorUndifined, errorResponse } from '../Utils/response';
import { getUserByToken } from '../Services/user.service';

exports.auth = async (req: Request, res: Response, next: any) => {
  try {
    const token: any = req.headers.authorization;
    if (!token) {
      res.status(401).send(errorUndifined(null, 'No Token'));
    } else {
      // const tk = String(token).split(' ');
      // const decoded: any = jwt.verify(tk[1], 'moneylabsecret');
      const user: any = await getUserByToken(String(token));
      if (user) {
        next();
      } else {
        res.status(401).send(errorUndifined(null, 'No Invalid'));
      }
    }
  } catch (err) {
    console.log(err);
    res.status(500).send(errorResponse(err, 'Token Invalid'));
  }
};
