FROM node:18-alpine AS deps
WORKDIR /src/app
COPY package.json ./
COPY package-lock.json ./
RUN apk add --no-cache openssl
RUN npm ci
COPY prisma ./prisma/
RUN npx prisma generate

# Rebuild the source code only when needed
FROM node:18-alpine AS builder
WORKDIR /src/app
COPY . .
COPY --from=deps /src/app/node_modules ./node_modules
RUN npm run build

# Production image, copy all the files and run nestjs
FROM node:18-alpine AS runner
WORKDIR /src/app
RUN apk add --no-cache openssl
#ENV NODE_ENV developmentK
#ENV ROUTES_PATH './dist/Routes'
#ENV PORT 4100
#ENV DATABASE_URL "postgresql://postgres:<EMAIL>:27337/railway"
#ENV DATABASE_URL "*************************************************/moneylabth?schema=public"
#ENV DATABASE_URL "postgresql://doadmin:<EMAIL>:25060/moneylabth?sslmode=require"


COPY --from=builder /src/app/dist ./dist
COPY --from=builder /src/app/node_modules ./node_modules
COPY --from=builder /src/app/package.json package.json
COPY --from=builder /src/app/tsconfig.json tsconfig.json
# COPY --from=builder /src/app/.env .env

EXPOSE 3000
CMD ["npm", "run", "start"]
