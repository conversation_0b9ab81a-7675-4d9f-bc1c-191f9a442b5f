{
  "compilerOptions": {
    "allowJs": true,
    "target": "es6",
    "lib": ["es5", "es6", "dom"],
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "module": "commonjs",
//    "module": "ES2022",
    "moduleResolution": "node",
    "baseUrl": "src/",
    "typeRoots": ["./src/types", "./node_modules/@types"],
    "resolveJsonModule": true,
    "outDir": "./dist",
    "removeComments": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "skipLibCheck": true
  },
  "include": ["./src/**/*.tsx", "./src/**/*.ts"],
  "exclude": ["node_modules", "test/**/*.ts"],
}