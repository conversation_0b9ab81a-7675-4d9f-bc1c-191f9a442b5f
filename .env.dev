# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

DATABASE_URL="*************************************************/moneylabth?schema=public"
#DATABASE_URL="**************************************************/postgres?schema=public"
#DATABASE_URL="postgresql://postgres:<EMAIL>:27337/railway"
#DATABASE_URL="postgresql://doadmin:<EMAIL>:25060/moneylabth?sslmode=require"
PORT=4100
#ROUTES_PATH='./dist/Routes'